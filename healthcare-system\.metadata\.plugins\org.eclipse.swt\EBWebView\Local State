{"accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "autofill": {"ablation_seed": "jI6KG8wJtPQ="}, "breadcrumbs": {"enabled": true, "enabled_time": "13396862129479116"}, "default_browser": {"browser_name_enum": 1}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "1752388670"}, "domain_actions_config": "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", "edge": {"manageability": {"edge_last_active_time": "13396862988683375"}, "mitigation_manager": {"renderer_app_container_compatible_count": 1, "renderer_code_integrity_compatible_count": 1}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"num_healthy_browsers_since_failure": 1}, "hardware_acceleration_mode_previous": true, "identity_combined_status": {"aad": 1, "ad": 1}, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": 1752388530942.628, "network": 1752388531000.0, "ticks": ***********.0, "uncertainty": 1200895.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.3351.83", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAB9WLFCa6cFSZCS1T86mzgrEAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAAAZizpfBoJNiMcU44kFpH3eKe0dF/Db39pgaMEiS3faCAAAAAAOgAAAAAIAACAAAADo+DhZRKW8s5Tu+E4rHUs3S8fbnpuP8ybuId1SP39rbjAAAADs8CaErfcNOZWdiK+s4SWzxbpe1rzoAnZ29V1ok68u9uE8zPouaXwb5MGrBJl96XJAAAAAPPiiGS9Ylkw0UtLJC6AW4it08U5nmnNXBO1g5iFVdC+qDl1JWe7S+rPM+89LDNg0oUfmQJ2SgivHzzC/FGuMuQ=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"active_time": **********.620503, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "", "edge_account_first_name": "", "edge_account_last_name": "", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "", "edge_account_type": 0, "edge_profile_can_be_deleted": true, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profile 1", "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": ["<PERSON><PERSON><PERSON>"], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 1}, "signin_last_seen_version": "138.0.3351.83", "signin_last_updated_time": **********.532474}, "sentinel_creation_time": "0", "session_id_generator_last_value": "********", "signin": {"active_accounts_last_emitted": "*****************"}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 2, "window_count_max": 2}, "telemetry_client": {"cloned_install": {"user_data_dir_id": ********}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 0}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVdlYlZpZXdcQXBwbGljYXRpb25cMTM4LjAuMzM1MS44M1x0ZWxjbGllbnQuZGxs", "sample_id": ********}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "", "cohortname": "", "installdate": -1}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "", "cohortname": "", "installdate": -1}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2025.5.15.1"}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "", "cohortname": "", "installdate": -1}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "", "cohortname": "", "installdate": -1}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "", "cohortname": "", "installdate": -1}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "", "cohortname": "", "installdate": -1}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "", "cohortname": "", "installdate": -1}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "", "cohortname": "", "installdate": -1}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "", "cohortname": "", "installdate": -1}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "6498.2024.12.2"}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "", "cohortname": "", "installdate": -1}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}C:\\Users\\<USER>", "diagnostics": {"last_data_collection_level_on_launch": 1}, "limited_entropy_randomization_source": "E2DE57B162E56831BB414DDC11C4C5D0", "low_entropy_source3": 559, "machine_id": 15985829, "payload_counter": 1, "pseudo_low_entropy_source": 3365, "reporting_enabled": false, "reset_client_id_deterministic": true, "session_id": 2, "stability": {"browser_last_live_timestamp": "13396862987411941", "exited_cleanly": true, "stats_buildtime": "1752102914", "stats_version": "138.0.3351.83-64", "system_crash_count": 0}}, "variations_compressed_seed": "H4sIAAAAAAAAAJVW25LaOBD9lSk9W1OW77C1DwyXhF3YZccQUtmkpoTdGNXYkkuSB0hq/n1LtmFgMs4mPFB065zTF7Ubf0PjYYz639D4kORVCuODBslpPhR8y7JpqqZ8JjLU17ICCzXemciWVGagUR9BmsGD0nSTA3q20DjNoAHVmmkGE5ZDfFQaikGSgFLGnzJlCBOgupKgUP9f9BoWH4uNyFkyY/xxuIPkEX15ttCfLM/jPdPJDq8UzWD8BFyPqKYdqoWa8rqsa/SUTwDSDW1VRy3xVQp12Jwp/ZMpj5iERAt5nGqQVDPB73LRKLxUMOZGZv3BGaQpMxiaL6QwdFMbpEvIoQAtjyYo8O8LWsPmA4P9//MvKlt/cJaMH9eMp2J/DzRn+tik1NW2DrjVeTTg6apMqYamyuIwKNlyJ4XWOZjMVH1rnfGuCa9yH9ICJK2vcC4400IynnVIzSFldCIqntY38Da1HqX4jiaPmTTQe1CikglMoDvHLrTR2j85MeXpRhw6yFMlcqqhBUE63UpagLqeiHvgKUiQg7IcCq4p4yA7G3Yag4b9JtWIr4XM02FVTnJB9fE06LUxFyl0yl9AjMxM0DTWVDOlWaJmIssYzxaSdfJfE/YLyVpaLRinya5JvWvOze4wqKYM2AwqvVtQpR7hqFbTethl/KOhajnANUvqWXhhN303AXaiLJtSEhiKoqQS7kGVgquLNfZWeifmipZsfCjJDyUH6Yzy7O8nkJKlvybs1sL/rKZDQyipmRsN0oC/IU4LQH2U7CjnkCMLPdG8Mp4JerbOx1CKZHdx6NrN5xIjQUvKVcF0vcofBH/YMwkPmhUgKv1QsDxnChLBU3Uh5RgVk+CptnrFqs47rU8HeS72M7NXv1gdFbXQk2oNfok6Z4kUSmz17Ro2d1LsFcjbhRRabKrt7erP+W2zlhdSbFkOlm0R/7efIGmq1e1MZLGmUlu25f0KyVx1DhpqXtOU8y5eiDKm5rj+bpfXWw2aCJnAmXZGW/UTlS1B6RVnWyGLEVNask1l5vo9U1pkkhbd/TSLq3TOaVzd4OUYpLClVa7fGfhb6Ctw9Vh0Sn55bh+HCZNK31f8ZehHYNo0uR+311OPA6QLeswFTTs3Sget/bsp9fHtaN93GWArE1QnGENWANf1djDg+WA6yHPUR1uaK1PSej2+dqik3FbGY9e/s4rK9GQ8mXVtW2juBv5QSLhmmr0MXMcgn1gC6lUc2HxcXrvuGM+uPabAa895PFfKxG79zxZ6DzStR+AbGi9phvroM/pUjj9Fo68rdRgOx97dQevq8LH6xKPxYTEdbqgaTBbr2F/Mv85//4xMvEPJ6p6huOLWDXFv/qjyG8d2/Bs77Lt+37Vv3s2XdW0V1/I4rP9R0PQvZCHzXFSq9dRr4vT2OB01eZnXTrTA95jYkeOHNiaYEGuBR9hz3CjCDg7Q9Qtlgw6c0OuF2MWR1djEIZ6NXew1tu8REhIc4F5rO5FjX5x7ke30etjFfmv7vcg3es7ZtkMPuye+54U94mAXkxbgEt+LCCY4aGzHd3uu+5IQCWzfdzDBbmu7rheGLwkQ4vouwd4pgB05xDYJtXq275mCyAlve70eic56QeSFHu6d0gnsgIQ4wg6pTc93QxdHOPLR2w8gOuUc+L2XnOzQjwIfezhErx+LhuCFrk0CTM5F+r1e5GNy6iIhbo/42PGbSzSSvch1vXObgsAOL4oI7DA8swM7ikzDmoo8146c8/14TugZFQc9P/8HOxMEQK0MAAA=", "variations_config_ids": "{\"ECS\":\"P-R-1082570-1-11,P-D-42388-2-6\",\"EdgeConfig\":\"P-R-1627497-3-8,P-R-1612140-3-4,P-R-1541171-6-9,P-R-1528200-3-4,P-R-1480299-3-5,P-R-1459857-3-2,P-R-1459074-3-9,P-R-1447912-3-12,P-R-1315481-1-6,P-R-1253933-3-8,P-R-1160552-1-3,P-R-1133477-3-4,P-R-1113531-4-9,P-R-1082109-3-6,P-R-1054140-1-4,P-R-1049918-1-3,P-R-68474-9-12,P-R-60617-8-21,P-R-45373-8-85\",\"EdgeFirstRunConfig\":\"P-R-1253659-3-4,P-R-1075865-4-7\",\"Segmentation\":\"P-R-1473016-1-8,P-R-1159985-1-5,P-R-1113915-25-11,P-R-1098334-1-6,P-R-66078-1-3,P-R-66077-1-5,P-R-60882-1-2,P-R-43082-3-5,P-R-42744-1-2\"}", "variations_country": "IN", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 0, "variations_google_groups": {"Default": []}, "variations_last_fetch_time": "13396862131836400", "variations_last_runtime_fetch_time": "13396862131839885", "variations_limited_entropy_synthetic_trial_seed_v2": "47", "variations_permanent_consistency_country": ["138.0.3351.83", "IN"], "variations_runtime_compressed_seed": "H4sIAAAAAAAAAG2QS4/TQBCE/0uf3WJ6ep6WOKAkkAVpk30gZYU5GGeIgrANtiMUIv/31XiS7EpwrO6qmf7qBLO2+b7f3cx7yOFUwGL2UEBewBrvkYST2gokJMrWOEcl2TmUaArIClhsd2He1uW+eVcN+7bpXwU9S6Vj0GdpwNaziQN1HkhlPF+env4yQiEhm0lbx2LyuyTZyEnaJElIjRqJJ2mYjEaFMmU1S8UoMXmVEJ6RL0t2Vim06NOWyXmP0qPyamIk0s6QQokXzY7Jxcuu1PeHZtjXIVX3ipqdNQIl+sn5EHZ1aIYydvNiUpYFRZQERqS9d7EqfdbEnjRK/dKMd8yxmgRgjLDTOVdlr2kjnJNIKBM5CyeRzzslIzihLGCE7F8OyE+wqH8Nx/9uQlN++xneh3I4dKGH/AuE0FXwdRwzWIZyG7o+2mbtoRm646zdBsjh5jZ+9FjuIIcCPn/8sXKbNS3p958PT1W1XNXBVn9vj6vHdXijAt3dPW26zafA/dsCYByfAX0/96idAgAA", "variations_seed_client_version_at_store": "138.0.3351.83", "variations_seed_date": "13396862130000000", "variations_seed_etag": "\"ZpEZ8DzUsxCCE4BxttuxXuZn8ExPICbasAFPWS5PMzM=\"", "variations_seed_milestone": 138, "variations_seed_runtime_etag": "\"UJjO8XP1H1qwGYccHOme7czNyOTPe/4e1QQYXrXKe3s=\"", "variations_seed_signature": "", "was": {"restarted": false}}