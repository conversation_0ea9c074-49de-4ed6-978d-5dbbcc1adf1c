!SESSION 2025-07-13 12:04:33.996 -----------------------------------------------
eclipse.buildId=4.36.0.20250605-1300
java.version=21.0.7
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_AU
Framework arguments:  -product org.eclipse.epp.package.java.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.java.product

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:04:35.685
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:05:24.095
!MESSAGE Logback config file: D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.100.20250418-1315.xml
!SESSION 2025-07-13 12:08:37.063 -----------------------------------------------
eclipse.buildId=4.36.0.20250605-1300
java.version=21.0.7
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_AU
Framework arguments:  -product org.eclipse.epp.package.java.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.java.product

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:08:39.354
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:08:44.956
!MESSAGE Logback config file: D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.100.20250418-1315.xml

!ENTRY org.eclipse.lsp4e 4 0 2025-07-13 12:08:55.707
!MESSAGE Unexpected null value present!
!STACK 0
java.lang.IllegalStateException: Unexpected null value present!
	at org.eclipse.lsp4e.internal.NullSafetyHelper.castNonNull(NullSafetyHelper.java:36)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticTokensClient.requestFullSemanticTokens(SemanticTokensClient.java:45)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.fullReconcile(SemanticHighlightReconcilerStrategy.java:239)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.initialReconcile(SemanticHighlightReconcilerStrategy.java:258)
	at org.eclipse.ui.internal.genericeditor.CompositeReconcilerStrategy.initialReconcile(CompositeReconcilerStrategy.java:48)
	at org.eclipse.jface.text.reconciler.Reconciler.initialProcess(Reconciler.java:223)
	at org.eclipse.jface.text.reconciler.AbstractReconciler$BackgroundThread.run(AbstractReconciler.java:177)

!ENTRY org.eclipse.epp.mpc.ui 2 0 2025-07-13 12:16:19.878
!MESSAGE Unable to load image https://marketplace.eclipse.org/sites/default/files/styles/badge_logo/public/Thymeleaf%20logo%20110x80_0.png?itok=h3JemsvI
!STACK 0
org.eclipse.swt.SWTException: Unsupported or unrecognized format
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.internal.image.FileFormat.lambda$11(FileFormat.java:132)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.eclipse.swt.internal.image.FileFormat.load(FileFormat.java:131)
	at org.eclipse.swt.internal.NativeImageLoader.load(NativeImageLoader.java:26)
	at org.eclipse.jface.resource.URLImageDescriptor.loadImageFromStream(URLImageDescriptor.java:152)
	at org.eclipse.jface.resource.URLImageDescriptor.getImageData(URLImageDescriptor.java:137)
	at org.eclipse.jface.resource.URLImageDescriptor.getImageData(URLImageDescriptor.java:109)
	at org.eclipse.jface.resource.URLImageDescriptor.lambda$2(URLImageDescriptor.java:74)
	at org.eclipse.swt.graphics.Image.<init>(Image.java:609)
	at org.eclipse.jface.resource.URLImageDescriptor.createImage(URLImageDescriptor.java:310)
	at org.eclipse.jface.resource.ImageDescriptor.createResource(ImageDescriptor.java:271)
	at org.eclipse.jface.resource.DeviceResourceManager.allocate(DeviceResourceManager.java:56)
	at org.eclipse.jface.resource.AbstractResourceManager.create(AbstractResourceManager.java:92)
	at org.eclipse.jface.resource.LazyResourceManager.create(LazyResourceManager.java:95)
	at org.eclipse.jface.resource.LocalResourceManager.allocate(LocalResourceManager.java:71)
	at org.eclipse.jface.resource.AbstractResourceManager.create(AbstractResourceManager.java:92)
	at org.eclipse.jface.resource.ResourceManager.createImage(ResourceManager.java:172)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceDiscoveryResources.safeCreateImage(MarketplaceDiscoveryResources.java:177)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceDiscoveryResources$1.lambda$0(MarketplaceDiscoveryResources.java:152)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.operation.ModalContext$ModalContextThread.block(ModalContext.java:167)
	at org.eclipse.jface.operation.ModalContext.run(ModalContext.java:369)
	at org.eclipse.jface.wizard.WizardDialog.run(WizardDialog.java:1035)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceViewer.doQuery(MarketplaceViewer.java:612)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceViewer.doQuery(MarketplaceViewer.java:533)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceViewer.doFind(MarketplaceViewer.java:395)
	at org.eclipse.epp.internal.mpc.ui.wizards.MarketplaceViewer.filterTextChanged(MarketplaceViewer.java:389)
	at org.eclipse.equinox.internal.p2.ui.discovery.util.FilteredViewer.lambda$2(FilteredViewer.java:108)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:303)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1223)
	at org.eclipse.swt.widgets.Control.traverse(Control.java:4286)
	at org.eclipse.swt.widgets.Control.translateTraversal(Control.java:4268)
	at org.eclipse.swt.widgets.Display.translateTraversal(Display.java:5024)
	at org.eclipse.swt.widgets.Display.filterMessage(Display.java:1358)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3720)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.epp.internal.mpc.ui.commands.AbstractMarketplaceWizardCommand.openWizardDialog(AbstractMarketplaceWizardCommand.java:146)
	at org.eclipse.epp.internal.mpc.ui.commands.AbstractMarketplaceWizardCommand.execute(AbstractMarketplaceWizardCommand.java:73)
	at org.eclipse.ui.internal.handlers.HandlerProxy.execute(HandlerProxy.java:277)
	at org.eclipse.ui.internal.handlers.E4HandlerProxy.execute(E4HandlerProxy.java:98)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:56)
	at org.eclipse.e4.core.internal.di.InjectorImpl.invokeUsingClass(InjectorImpl.java:299)
	at org.eclipse.e4.core.internal.di.InjectorImpl.invoke(InjectorImpl.java:233)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.invoke(ContextInjectionFactory.java:174)
	at org.eclipse.e4.core.commands.internal.HandlerServiceHandler.execute(HandlerServiceHandler.java:165)
	at org.eclipse.core.commands.Command.executeWithChecks(Command.java:488)
	at org.eclipse.core.commands.ParameterizedCommand.executeWithChecks(ParameterizedCommand.java:485)
	at org.eclipse.e4.core.commands.internal.HandlerServiceImpl.executeHandler(HandlerServiceImpl.java:204)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.executeItem(HandledContributionItem.java:444)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.handleWidgetSelection(AbstractContributionItem.java:475)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:497)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4136)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3724)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.lsp4e 4 0 2025-07-13 12:18:16.012
!MESSAGE Unexpected null value present!
!STACK 0
java.lang.IllegalStateException: Unexpected null value present!
	at org.eclipse.lsp4e.internal.NullSafetyHelper.castNonNull(NullSafetyHelper.java:36)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticTokensClient.requestFullSemanticTokens(SemanticTokensClient.java:45)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.fullReconcile(SemanticHighlightReconcilerStrategy.java:239)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.fullReconcileOnce(SemanticHighlightReconcilerStrategy.java:278)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.reconcile(SemanticHighlightReconcilerStrategy.java:263)
	at org.eclipse.ui.internal.genericeditor.CompositeReconcilerStrategy.reconcile(CompositeReconcilerStrategy.java:63)
	at org.eclipse.jface.text.reconciler.Reconciler.process(Reconciler.java:149)
	at org.eclipse.jface.text.reconciler.AbstractReconciler$BackgroundThread.run(AbstractReconciler.java:207)
!SESSION 2025-07-13 12:18:36.698 -----------------------------------------------
eclipse.buildId=4.36.0.20250605-1300
java.version=21.0.7
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_AU
Framework arguments:  -product org.eclipse.epp.package.java.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.java.product -data file:/D:/D_DRIVE/Documents/GitHub/josh/healthcare-system/

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:18:39.601
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-07-13 12:18:41.861
!MESSAGE Logback config file: D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.100.20250418-1315.xml

!ENTRY org.eclipse.lsp4e 4 0 2025-07-13 12:18:47.982
!MESSAGE Unexpected null value present!
!STACK 0
java.lang.IllegalStateException: Unexpected null value present!
	at org.eclipse.lsp4e.internal.NullSafetyHelper.castNonNull(NullSafetyHelper.java:36)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticTokensClient.requestFullSemanticTokens(SemanticTokensClient.java:45)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.fullReconcile(SemanticHighlightReconcilerStrategy.java:239)
	at org.eclipse.lsp4e.operations.semanticTokens.SemanticHighlightReconcilerStrategy.initialReconcile(SemanticHighlightReconcilerStrategy.java:258)
	at org.eclipse.ui.internal.genericeditor.CompositeReconcilerStrategy.initialReconcile(CompositeReconcilerStrategy.java:48)
	at org.eclipse.jface.text.reconciler.Reconciler.initialProcess(Reconciler.java:223)
	at org.eclipse.jface.text.reconciler.AbstractReconciler$BackgroundThread.run(AbstractReconciler.java:177)
