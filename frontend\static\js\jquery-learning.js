/**
 * jQuery Learning for Healthcare System
 * This file contains hands-on exercises to learn jQuery fundamentals
 */

// Wait for document to be ready
$(document).ready(function() {
    console.log("🏥 Healthcare jQuery Learning System Loaded!");
    
    // Initialize the learning environment
    initializeLearningEnvironment();
});

/**
 * Initialize the learning environment
 */
function initializeLearningEnvironment() {
    logEvent("System initialized - Ready for jQuery learning!");
    
    // Add some sample data
    window.samplePatients = [
        { id: 'P001', name: '<PERSON>', age: 35, phone: '555-0101', status: 'Active' },
        { id: 'P002', name: '<PERSON>', age: 28, phone: '555-0102', status: 'Active' },
        { id: 'P003', name: '<PERSON>', age: 45, phone: '555-0103', status: 'Emergency' },
        { id: 'P004', name: '<PERSON>', age: 32, phone: '555-0104', status: 'Active' },
        { id: 'P005', name: '<PERSON>', age: 67, phone: '555-0105', status: 'Critical' }
    ];
    
    window.appointments = [];
}

// =============================================================================
// EXERCISE 1: JQUERY SELECTORS
// =============================================================================

/**
 * Select element by ID
 */
function selectById() {
    // Reset previous selections
    $('.patient-card').removeClass('highlight');
    
    // Select by ID using jQuery
    $('#patient-john').addClass('highlight');
    
    // Update result
    $('#selector-result').html(`
        <strong>Selected:</strong> $('#patient-john')<br>
        <em>This selects the element with ID 'patient-john'</em>
    `);
    
    logEvent("Selected patient by ID: #patient-john");
}

/**
 * Select elements by class
 */
function selectByClass() {
    // Reset previous selections
    $('.patient-card').removeClass('highlight');
    
    // Select by class using jQuery
    $('.patient-card').addClass('highlight');
    
    // Count selected elements
    const count = $('.patient-card').length;
    
    $('#selector-result').html(`
        <strong>Selected:</strong> $('.patient-card')<br>
        <em>This selects all ${count} elements with class 'patient-card'</em>
    `);
    
    logEvent(`Selected ${count} patients by class: .patient-card`);
}

/**
 * Select by attribute
 */
function selectByAttribute() {
    // Reset previous selections
    $('.patient-card').removeClass('highlight');
    
    // Select by attribute using jQuery
    $('.patient-card.emergency').addClass('highlight');
    
    $('#selector-result').html(`
        <strong>Selected:</strong> $('.patient-card.emergency')<br>
        <em>This selects elements with both 'patient-card' and 'emergency' classes</em>
    `);
    
    logEvent("Selected emergency patient by combined class selector");
}

/**
 * Reset selection highlighting
 */
function resetSelection() {
    $('.patient-card').removeClass('highlight');
    $('#selector-result').html('Results will appear here...');
    logEvent("Selection reset");
}

// =============================================================================
// EXERCISE 2: DOM MANIPULATION
// =============================================================================

/**
 * Update patient information
 */
function updatePatientInfo() {
    // Use jQuery to update text content
    $('#patient-name span').text('John Doe');
    $('#patient-age span').text('35 years');
    $('#patient-status span').text('Active').css('color', 'green');
    
    // Update result
    $('#dom-result').html(`
        <strong>Updated:</strong> Patient information using .text() and .css()<br>
        <em>jQuery methods: .text(), .css()</em>
    `);
    
    logEvent("Updated patient information using DOM manipulation");
}

/**
 * Add appointment to the list
 */
function addAppointment() {
    const appointmentTime = new Date().toLocaleTimeString();
    const appointmentHtml = `
        <div class="alert alert-info alert-sm mb-2">
            <strong>Dr. Smith</strong> - ${appointmentTime}
            <button class="btn btn-sm btn-outline-danger float-end" onclick="$(this).parent().remove()">×</button>
        </div>
    `;
    
    // Use jQuery to append content
    $('#appointment-list').append(appointmentHtml);
    
    $('#dom-result').html(`
        <strong>Added:</strong> New appointment using .append()<br>
        <em>jQuery method: .append()</em>
    `);
    
    logEvent("Added new appointment to the list");
}

/**
 * Toggle visibility of elements
 */
function hideShowElements() {
    // Toggle visibility using jQuery
    $('#patient-info').toggle();
    
    const isVisible = $('#patient-info').is(':visible');
    
    $('#dom-result').html(`
        <strong>Toggled:</strong> Patient info is now ${isVisible ? 'visible' : 'hidden'}<br>
        <em>jQuery methods: .toggle(), .is(':visible')</em>
    `);
    
    logEvent(`Toggled patient info visibility: ${isVisible ? 'shown' : 'hidden'}`);
}

/**
 * Clear all appointments
 */
function clearAppointments() {
    // Remove all appointment alerts
    $('#appointment-list .alert').remove();
    
    $('#dom-result').html(`
        <strong>Cleared:</strong> All appointments removed using .remove()<br>
        <em>jQuery method: .remove()</em>
    `);
    
    logEvent("Cleared all appointments from the list");
}

// =============================================================================
// EXERCISE 3: EVENT HANDLING
// =============================================================================

/**
 * Setup event listeners for the form
 */
function setupEventListeners() {
    // Remove existing event listeners to avoid duplicates
    $('#patient-form').off('submit');
    $('#clear-form').off('click');
    $('#new-patient-name, #new-patient-age, #new-patient-phone').off('input');
    
    // Form submission event
    $('#patient-form').on('submit', function(e) {
        e.preventDefault(); // Prevent actual form submission
        
        const name = $('#new-patient-name').val();
        const age = $('#new-patient-age').val();
        const phone = $('#new-patient-phone').val();
        
        if (name && age && phone) {
            logEvent(`✅ Patient registered: ${name}, Age: ${age}, Phone: ${phone}`);
            
            // Clear form after successful registration
            this.reset();
        } else {
            logEvent('❌ Registration failed: Please fill all fields');
        }
    });
    
    // Clear form button event
    $('#clear-form').on('click', function() {
        $('#patient-form')[0].reset();
        logEvent('🗑️ Form cleared');
    });
    
    // Input field events
    $('#new-patient-name').on('input', function() {
        logEvent(`📝 Name field updated: ${$(this).val()}`);
    });
    
    $('#new-patient-age').on('input', function() {
        logEvent(`📝 Age field updated: ${$(this).val()}`);
    });
    
    $('#new-patient-phone').on('input', function() {
        logEvent(`📝 Phone field updated: ${$(this).val()}`);
    });
    
    logEvent("✅ Event listeners setup complete!");
}

/**
 * Clear the event log
 */
function clearEventLog() {
    $('#event-log').html('Events will be logged here...');
}

/**
 * Log events to the event log
 */
function logEvent(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `<div><small class="text-muted">[${timestamp}]</small> ${message}</div>`;
    
    $('#event-log').append(logEntry);
    
    // Auto-scroll to bottom
    const logElement = document.getElementById('event-log');
    logElement.scrollTop = logElement.scrollHeight;
}

// =============================================================================
// EXERCISE 4: AJAX SIMULATION
// =============================================================================

/**
 * Search patients (simulated AJAX)
 */
function searchPatients() {
    const searchTerm = $('#search-input').val().toLowerCase();
    
    // Show loading state
    $('#search-results').html('<div class="text-center"><em>Searching...</em></div>');
    
    // Simulate AJAX delay
    setTimeout(function() {
        const filteredPatients = window.samplePatients.filter(patient => 
            patient.name.toLowerCase().includes(searchTerm) ||
            patient.id.toLowerCase().includes(searchTerm)
        );
        
        let resultsHtml = '';
        if (filteredPatients.length > 0) {
            resultsHtml = '<h6>Search Results:</h6>';
            filteredPatients.forEach(patient => {
                resultsHtml += `
                    <div class="patient-card">
                        <strong>${patient.name}</strong> (${patient.id})<br>
                        Age: ${patient.age}, Status: ${patient.status}
                    </div>
                `;
            });
        } else {
            resultsHtml = '<div class="alert alert-warning">No patients found</div>';
        }
        
        $('#search-results').html(resultsHtml);
        
        logEvent(`🔍 Search completed: Found ${filteredPatients.length} patients for "${searchTerm}"`);
    }, 1000);
}

/**
 * Simulate loading patient data
 */
function loadPatientData() {
    $('#ajax-result').html('<div class="text-center"><em>Loading patient data...</em></div>');
    
    // Simulate AJAX request
    setTimeout(function() {
        $('#ajax-result').html(`
            <strong>✅ Success:</strong> Loaded ${window.samplePatients.length} patients<br>
            <em>Simulated: $.get('/api/patients/')</em>
        `);
        
        logEvent(`📥 Loaded ${window.samplePatients.length} patients from API`);
    }, 1500);
}

/**
 * Simulate saving patient data
 */
function savePatientData() {
    $('#ajax-result').html('<div class="text-center"><em>Saving patient data...</em></div>');
    
    // Simulate AJAX request
    setTimeout(function() {
        $('#ajax-result').html(`
            <strong>✅ Success:</strong> Patient data saved successfully<br>
            <em>Simulated: $.post('/api/patients/', data)</em>
        `);
        
        logEvent('💾 Patient data saved successfully');
    }, 1200);
}

/**
 * Simulate API error
 */
function simulateError() {
    $('#ajax-result').html('<div class="text-center"><em>Processing request...</em></div>');
    
    // Simulate AJAX error
    setTimeout(function() {
        $('#ajax-result').html(`
            <strong>❌ Error:</strong> Failed to connect to server<br>
            <em>Status: 500 Internal Server Error</em>
        `).css('background-color', '#f8d7da');
        
        logEvent('❌ API request failed: Server error');
    }, 800);
}

// =============================================================================
// EXERCISE 5: HEALTHCARE-SPECIFIC FEATURES
// =============================================================================

/**
 * Schedule an appointment
 */
function scheduleAppointment() {
    const date = $('#appointment-date').val();
    const time = $('#appointment-time').val();
    
    if (!date || !time) {
        alert('Please select both date and time');
        return;
    }
    
    const appointment = {
        id: 'APT' + (window.appointments.length + 1).toString().padStart(3, '0'),
        date: date,
        time: time,
        patient: 'John Doe',
        doctor: 'Dr. Smith',
        status: 'Scheduled'
    };
    
    window.appointments.push(appointment);
    
    const appointmentHtml = `
        <div class="alert alert-success alert-sm mb-2">
            <strong>${appointment.patient}</strong> with ${appointment.doctor}<br>
            <small>${appointment.date} at ${appointment.time}</small>
            <button class="btn btn-sm btn-outline-danger float-end" onclick="cancelAppointment('${appointment.id}')">Cancel</button>
        </div>
    `;
    
    $('#scheduled-appointments').append(appointmentHtml);
    
    // Clear form
    $('#appointment-date, #appointment-time').val('');
    
    logEvent(`📅 Appointment scheduled: ${appointment.date} at ${appointment.time}`);
}

/**
 * Cancel an appointment
 */
function cancelAppointment(appointmentId) {
    // Remove from appointments array
    window.appointments = window.appointments.filter(apt => apt.id !== appointmentId);
    
    // Remove from DOM
    $(event.target).closest('.alert').remove();
    
    logEvent(`❌ Appointment cancelled: ${appointmentId}`);
}

/**
 * Show today's appointments
 */
function showTodayAppointments() {
    const today = new Date().toISOString().split('T')[0];
    const todayAppointments = window.appointments.filter(apt => apt.date === today);
    
    if (todayAppointments.length === 0) {
        $('#scheduled-appointments').html('<div class="alert alert-info">No appointments for today</div>');
    } else {
        let html = '<h6>Today\'s Appointments:</h6>';
        todayAppointments.forEach(apt => {
            html += `
                <div class="alert alert-primary alert-sm mb-2">
                    <strong>${apt.patient}</strong> with ${apt.doctor}<br>
                    <small>Today at ${apt.time}</small>
                </div>
            `;
        });
        $('#scheduled-appointments').html(html);
    }
    
    logEvent(`📋 Showing today's appointments: ${todayAppointments.length} found`);
}

/**
 * Show upcoming appointments
 */
function showUpcomingAppointments() {
    const today = new Date().toISOString().split('T')[0];
    const upcomingAppointments = window.appointments.filter(apt => apt.date > today);
    
    if (upcomingAppointments.length === 0) {
        $('#scheduled-appointments').html('<div class="alert alert-info">No upcoming appointments</div>');
    } else {
        let html = '<h6>Upcoming Appointments:</h6>';
        upcomingAppointments.forEach(apt => {
            html += `
                <div class="alert alert-warning alert-sm mb-2">
                    <strong>${apt.patient}</strong> with ${apt.doctor}<br>
                    <small>${apt.date} at ${apt.time}</small>
                </div>
            `;
        });
        $('#scheduled-appointments').html(html);
    }
    
    logEvent(`📅 Showing upcoming appointments: ${upcomingAppointments.length} found`);
}
