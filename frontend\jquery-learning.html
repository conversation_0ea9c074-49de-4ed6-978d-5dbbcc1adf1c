<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>jQuery Learning for Healthcare System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .exercise-section {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8f9fa;
        }
        .result-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            min-height: 40px;
        }
        .patient-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background-color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .patient-card:hover {
            background-color: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .hidden {
            display: none;
        }
        .highlight {
            background-color: #fff3cd !important;
            border-color: #ffeaa7 !important;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center text-primary mb-4">🏥 jQuery Learning for Healthcare System</h1>
        
        <!-- Exercise 1: Basic Selectors -->
        <div class="exercise-section">
            <h3>📍 Exercise 1: jQuery Selectors</h3>
            <p>Learn how to select HTML elements using jQuery</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Patient List:</h5>
                    <div id="patient-john" class="patient-card">
                        <strong>John Doe</strong><br>
                        Age: 35, ID: P001
                    </div>
                    <div id="patient-jane" class="patient-card">
                        <strong>Jane Smith</strong><br>
                        Age: 28, ID: P002
                    </div>
                    <div class="patient-card emergency">
                        <strong>Bob Johnson</strong><br>
                        Age: 45, ID: P003 (Emergency)
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Try These Selectors:</h5>
                    <button class="btn btn-primary btn-sm" onclick="selectById()">Select by ID (#patient-john)</button><br><br>
                    <button class="btn btn-success btn-sm" onclick="selectByClass()">Select by Class (.patient-card)</button><br><br>
                    <button class="btn btn-warning btn-sm" onclick="selectByAttribute()">Select Emergency Patient</button><br><br>
                    <button class="btn btn-secondary btn-sm" onclick="resetSelection()">Reset</button>
                    
                    <div class="result-box mt-3" id="selector-result">
                        Results will appear here...
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercise 2: DOM Manipulation -->
        <div class="exercise-section">
            <h3>🔧 Exercise 2: DOM Manipulation</h3>
            <p>Learn to modify HTML content dynamically</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Patient Information:</h5>
                    <div id="patient-info" class="patient-card">
                        <div id="patient-name">Patient Name: <span>Not Selected</span></div>
                        <div id="patient-age">Age: <span>--</span></div>
                        <div id="patient-status">Status: <span>Unknown</span></div>
                    </div>
                    
                    <div id="appointment-list" class="mt-3">
                        <h6>Today's Appointments:</h6>
                        <!-- Appointments will be added here -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>DOM Manipulation Actions:</h5>
                    <button class="btn btn-primary btn-sm" onclick="updatePatientInfo()">Update Patient Info</button><br><br>
                    <button class="btn btn-success btn-sm" onclick="addAppointment()">Add Appointment</button><br><br>
                    <button class="btn btn-warning btn-sm" onclick="hideShowElements()">Toggle Visibility</button><br><br>
                    <button class="btn btn-danger btn-sm" onclick="clearAppointments()">Clear Appointments</button>
                    
                    <div class="result-box mt-3" id="dom-result">
                        DOM manipulation results...
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercise 3: Event Handling -->
        <div class="exercise-section">
            <h3>⚡ Exercise 3: Event Handling</h3>
            <p>Learn to handle user interactions</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Patient Registration Form:</h5>
                    <form id="patient-form">
                        <div class="mb-3">
                            <label for="new-patient-name" class="form-label">Patient Name:</label>
                            <input type="text" class="form-control" id="new-patient-name" placeholder="Enter patient name">
                        </div>
                        <div class="mb-3">
                            <label for="new-patient-age" class="form-label">Age:</label>
                            <input type="number" class="form-control" id="new-patient-age" placeholder="Enter age">
                        </div>
                        <div class="mb-3">
                            <label for="new-patient-phone" class="form-label">Phone:</label>
                            <input type="tel" class="form-control" id="new-patient-phone" placeholder="Enter phone number">
                        </div>
                        <button type="submit" class="btn btn-primary">Register Patient</button>
                        <button type="button" class="btn btn-secondary" id="clear-form">Clear Form</button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h5>Event Log:</h5>
                    <div id="event-log" class="result-box" style="height: 200px; overflow-y: auto;">
                        Events will be logged here...
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-info btn-sm" onclick="setupEventListeners()">Setup Event Listeners</button>
                        <button class="btn btn-secondary btn-sm" onclick="clearEventLog()">Clear Log</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercise 4: AJAX Simulation -->
        <div class="exercise-section">
            <h3>🌐 Exercise 4: AJAX Simulation</h3>
            <p>Learn to make asynchronous requests (simulated)</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Patient Search:</h5>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="search-input" placeholder="Search patients...">
                        <button class="btn btn-primary mt-2" onclick="searchPatients()">Search Patients</button>
                    </div>
                    
                    <div id="search-results" class="mt-3">
                        <!-- Search results will appear here -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>API Simulation:</h5>
                    <button class="btn btn-success btn-sm" onclick="loadPatientData()">Load Patient Data</button><br><br>
                    <button class="btn btn-info btn-sm" onclick="savePatientData()">Save Patient Data</button><br><br>
                    <button class="btn btn-warning btn-sm" onclick="simulateError()">Simulate API Error</button>
                    
                    <div class="result-box mt-3" id="ajax-result">
                        AJAX results will appear here...
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercise 5: Healthcare-Specific Features -->
        <div class="exercise-section">
            <h3>🏥 Exercise 5: Healthcare-Specific jQuery</h3>
            <p>Real-world healthcare system interactions</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Appointment Scheduler:</h5>
                    <div class="mb-3">
                        <label for="appointment-date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="appointment-date">
                    </div>
                    <div class="mb-3">
                        <label for="appointment-time" class="form-label">Time:</label>
                        <select class="form-control" id="appointment-time">
                            <option value="">Select time...</option>
                            <option value="09:00">09:00 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="14:00">02:00 PM</option>
                            <option value="15:00">03:00 PM</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="scheduleAppointment()">Schedule Appointment</button>
                </div>
                <div class="col-md-6">
                    <h5>Scheduled Appointments:</h5>
                    <div id="scheduled-appointments" class="result-box" style="height: 150px; overflow-y: auto;">
                        No appointments scheduled...
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-info btn-sm" onclick="showTodayAppointments()">Today's Appointments</button>
                        <button class="btn btn-warning btn-sm" onclick="showUpcomingAppointments()">Upcoming</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Our jQuery Learning Script -->
    <script src="static/js/jquery-learning.js"></script>
</body>
</html>
