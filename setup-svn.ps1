# Healthcare Management System - SVN Setup Script
# This PowerShell script helps set up SVN repository structure

Write-Host "🏥 Healthcare Management System - SVN Setup" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check if TortoiseSVN is installed
$tortoiseSvnPath = "C:\Program Files\TortoiseSVN\bin\svn.exe"
if (-not (Test-Path $tortoiseSvnPath)) {
    Write-Host "❌ TortoiseSVN not found!" -ForegroundColor Red
    Write-Host "Please install TortoiseSVN from: https://tortoisesvn.net/downloads.html" -ForegroundColor Yellow
    Write-Host "After installation, restart your computer and run this script again." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ TortoiseSVN found!" -ForegroundColor Green

# Create SVN repository directory
$repoPath = "D:\SVN-Repos\healthcare-system"
Write-Host "📁 Creating repository directory: $repoPath" -ForegroundColor Cyan

try {
    New-Item -ItemType Directory -Path $repoPath -Force | Out-Null
    Write-Host "✅ Repository directory created" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create repository directory: $_" -ForegroundColor Red
    exit 1
}

# Create SVN repository
Write-Host "🔧 Creating SVN repository..." -ForegroundColor Cyan
try {
    & $tortoiseSvnPath admin create $repoPath
    Write-Host "✅ SVN repository created successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create SVN repository: $_" -ForegroundColor Red
    exit 1
}

# Create temporary structure for import
$tempStructure = "D:\temp-svn-structure"
Write-Host "📂 Creating temporary SVN structure..." -ForegroundColor Cyan

# Remove existing temp structure if it exists
if (Test-Path $tempStructure) {
    Remove-Item -Path $tempStructure -Recurse -Force
}

# Create standard SVN structure
New-Item -ItemType Directory -Path "$tempStructure\trunk" -Force | Out-Null
New-Item -ItemType Directory -Path "$tempStructure\branches" -Force | Out-Null
New-Item -ItemType Directory -Path "$tempStructure\tags" -Force | Out-Null

# Create README files for each directory
@"
# Trunk Directory
This is the main development line for the Healthcare Management System.

## Structure:
- backend/: Django application
- frontend/: jQuery, HTML, CSS files
- database/: SQL scripts and migrations
- docs/: Project documentation
- pom.xml: Maven configuration
"@ | Out-File -FilePath "$tempStructure\trunk\README.md" -Encoding UTF8

@"
# Branches Directory
This directory contains feature branches and development branches.

## Naming Convention:
- feature-[feature-name]: New features
- bugfix-[bug-description]: Bug fixes
- release-[version]: Release preparation

## Examples:
- feature-patient-registration
- feature-appointment-scheduling
- bugfix-login-validation
- release-1.0.0
"@ | Out-File -FilePath "$tempStructure\branches\README.md" -Encoding UTF8

@"
# Tags Directory
This directory contains release tags and version snapshots.

## Naming Convention:
- v[major].[minor].[patch]: Release versions
- release-[date]: Date-based releases

## Examples:
- v1.0.0: First major release
- v1.1.0: Minor feature update
- v1.1.1: Patch release
"@ | Out-File -FilePath "$tempStructure\tags\README.md" -Encoding UTF8

Write-Host "✅ Temporary structure created" -ForegroundColor Green

# Import structure to SVN
Write-Host "📤 Importing structure to SVN repository..." -ForegroundColor Cyan
$repoUrl = "file:///$($repoPath.Replace('\', '/'))"

try {
    Set-Location $tempStructure
    & $tortoiseSvnPath import . $repoUrl -m "Initial repository structure for Healthcare Management System"
    Write-Host "✅ Structure imported successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to import structure: $_" -ForegroundColor Red
    exit 1
}

# Clean up temporary structure
Remove-Item -Path $tempStructure -Recurse -Force
Write-Host "🧹 Cleaned up temporary files" -ForegroundColor Green

# Create working copy directory
$workingCopyPath = "D:\D_DRIVE\Documents\GitHub\josh\healthcare-svn"
Write-Host "📁 Creating working copy directory: $workingCopyPath" -ForegroundColor Cyan

try {
    New-Item -ItemType Directory -Path $workingCopyPath -Force | Out-Null
    Write-Host "✅ Working copy directory created" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create working copy directory: $_" -ForegroundColor Red
    exit 1
}

# Checkout trunk to working copy
Write-Host "📥 Checking out trunk to working copy..." -ForegroundColor Cyan
$trunkUrl = "$repoUrl/trunk"

try {
    Set-Location $workingCopyPath
    & $tortoiseSvnPath checkout $trunkUrl .
    Write-Host "✅ Trunk checked out successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to checkout trunk: $_" -ForegroundColor Red
    exit 1
}

# Display summary
Write-Host ""
Write-Host "🎉 SVN Setup Complete!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Repository Location: $repoPath" -ForegroundColor Cyan
Write-Host "📍 Repository URL: $repoUrl" -ForegroundColor Cyan
Write-Host "📍 Working Copy: $workingCopyPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Copy your healthcare project files to: $workingCopyPath" -ForegroundColor White
Write-Host "2. Right-click in the working copy folder and select 'SVN Add'" -ForegroundColor White
Write-Host "3. Right-click and select 'SVN Commit' to commit your files" -ForegroundColor White
Write-Host "4. Configure Eclipse SVN integration" -ForegroundColor White
Write-Host ""
Write-Host "📚 Useful SVN Commands:" -ForegroundColor Yellow
Write-Host "- svn status: Check file status" -ForegroundColor White
Write-Host "- svn add .: Add all new files" -ForegroundColor White
Write-Host "- svn commit -m 'message': Commit changes" -ForegroundColor White
Write-Host "- svn update: Get latest changes" -ForegroundColor White
Write-Host ""
Write-Host "🏥 Your Healthcare Management System is ready for version control!" -ForegroundColor Green
