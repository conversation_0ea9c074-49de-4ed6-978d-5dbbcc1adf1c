<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <!-- Project Information -->
    <groupId>com.healthcare</groupId>
    <artifactId>patient-management-system</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    
    <name>Healthcare Patient Management System</name>
    <description>A comprehensive healthcare management system built with Django, jQuery, and PostgreSQL</description>
    
    <!-- Properties -->
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jquery.version>3.6.0</jquery.version>
        <bootstrap.version>5.1.3</bootstrap.version>
    </properties>
    
    <!-- Dependencies for Frontend Asset Management -->
    <dependencies>
        <!-- jQuery -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>jquery</artifactId>
            <version>${jquery.version}</version>
        </dependency>
        
        <!-- Bootstrap for styling -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>bootstrap</artifactId>
            <version>${bootstrap.version}</version>
        </dependency>
        
        <!-- Font Awesome for icons -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>font-awesome</artifactId>
            <version>6.0.0</version>
        </dependency>
        
        <!-- Chart.js for analytics dashboard -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>chartjs</artifactId>
            <version>3.7.0</version>
        </dependency>
    </dependencies>
    
    <!-- Build Configuration -->
    <build>
        <plugins>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            
            <!-- Frontend Maven Plugin for managing frontend assets -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.12.1</version>
                <configuration>
                    <workingDirectory>frontend</workingDirectory>
                    <installDirectory>target</installDirectory>
                </configuration>
                <executions>
                    <!-- Install Node.js and npm -->
                    <execution>
                        <id>install node and npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <configuration>
                            <nodeVersion>v16.14.0</nodeVersion>
                            <npmVersion>8.3.1</npmVersion>
                        </configuration>
                    </execution>
                    
                    <!-- Install frontend dependencies -->
                    <execution>
                        <id>npm install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                    
                    <!-- Build frontend assets -->
                    <execution>
                        <id>npm run build</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>run build</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Copy WebJars to static directory -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>unpack-webjars</id>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>org.webjars</includeGroupIds>
                            <outputDirectory>${project.build.directory}/webjars</outputDirectory>
                            <includes>META-INF/resources/webjars/**</includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <!-- Profiles for different environments -->
    <profiles>
        <profile>
            <id>development</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <environment>development</environment>
            </properties>
        </profile>
        
        <profile>
            <id>production</id>
            <properties>
                <environment>production</environment>
            </properties>
        </profile>
    </profiles>
    
</project>
