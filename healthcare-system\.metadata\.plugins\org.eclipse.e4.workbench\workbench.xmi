<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_jrUzsV-zEfC6v511D2elDw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_jrUzsl-zEfC6v511D2elDw" bindingContexts="_jrUzu1-zEfC6v511D2elDw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;patient-management-system/pom.xml&quot;>&#xD;&#xA;&lt;persistable path=&quot;/patient-management-system/pom.xml&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_jrUzsl-zEfC6v511D2elDw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_j7OQ41-zEfC6v511D2elDw" label="%trimmedwindow.label.eclipseSDK" x="30" y="138" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1752388528298"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_j7OQ41-zEfC6v511D2elDw" selectedElement="_j7OQ5F-zEfC6v511D2elDw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_j7OQ5F-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_kFPvk1-zEfC6v511D2elDw">
        <children xsi:type="advanced:Perspective" xmi:id="_kFPvk1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_kFPvlF-zEfC6v511D2elDw" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.svg">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.svg</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$Ctrl+Shift+T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_kFPvlF-zEfC6v511D2elDw" selectedElement="_kFPvnV-zEfC6v511D2elDw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_kFPvlV-zEfC6v511D2elDw" containerData="2500" selectedElement="_kFPvll-zEfC6v511D2elDw">
              <children xsi:type="basic:PartStack" xmi:id="_kFPvll-zEfC6v511D2elDw" elementId="left" containerData="6000" selectedElement="_kFPvl1-zEfC6v511D2elDw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvl1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_kFHMsF-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvmF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_kFHMsV-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvmV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_kFHMsl-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvml-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_kFPvkV-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_kFPvm1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvnF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_kFN6YV-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_kFPvnV-zEfC6v511D2elDw" containerData="7500" selectedElement="_kFPvnl-zEfC6v511D2elDw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_kFPvnl-zEfC6v511D2elDw" containerData="7500" selectedElement="_kFPvn1-zEfC6v511D2elDw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvn1-zEfC6v511D2elDw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_kE9bsF-zEfC6v511D2elDw"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_kFPvoF-zEfC6v511D2elDw" containerData="2500">
                  <children xsi:type="basic:PartStack" xmi:id="_kFPvoV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_kFPvol-zEfC6v511D2elDw">
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvol-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_kFN6YF-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_kFPvo1-zEfC6v511D2elDw" elementId="right" containerData="5000" selectedElement="_kFPvpF-zEfC6v511D2elDw">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvpF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ContentOutline" ref="_kFHMul-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvpV-zEfC6v511D2elDw" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_kFHMu1-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvpl-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_kFHMvF-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvp1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_kFPvkF-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_kFPvqF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_kFPvkl-zEfC6v511D2elDw" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_kFPvqV-zEfC6v511D2elDw" elementId="bottom" containerData="2500" selectedElement="_kFPvrl-zEfC6v511D2elDw">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvql-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProblemView" ref="_kFHMs1-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvq1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavadocView" ref="_kFHMtF-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvrF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.SourceView" ref="_kFHMtV-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvrV-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_kFHMtl-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvrl-zEfC6v511D2elDw" elementId="org.eclipse.ui.console.ConsoleView" ref="_kFHMt1-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvr1-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_kFHMuF-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvsF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_kFHMuV-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_kFPvsV-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_kFN6Yl-zEfC6v511D2elDw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_j7OQ5V-zEfC6v511D2elDw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_j7OQ5l-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_j7OQ4F-zEfC6v511D2elDw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_j7OQ51-zEfC6v511D2elDw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_j7OQ4V-zEfC6v511D2elDw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_j7OQ6F-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_j7OQ4l-zEfC6v511D2elDw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_j7OQ4F-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_j7OQ4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_kXTI4F-zEfC6v511D2elDw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kXTI4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_j7OQ4l-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_kE9bsF-zEfC6v511D2elDw" elementId="org.eclipse.ui.editorss" selectedElement="_kE9bsV-zEfC6v511D2elDw">
      <children xsi:type="basic:PartStack" xmi:id="_kE9bsV-zEfC6v511D2elDw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_C_NGUF-0EfCHSK8Zq27fFg">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_C_NGUF-0EfCHSK8Zq27fFg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="patient-management-system/pom.xml" iconURI="platform:/plugin/org.eclipse.m2e.editor/icons/editor-pom.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; partName=&quot;patient-management-system/pom.xml&quot; title=&quot;patient-management-system/pom.xml&quot; tooltip=&quot;patient-management-system/pom.xml&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/patient-management-system/pom.xml&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.m2e.editor.MavenPomEditor</tags>
          <tags>active</tags>
          <tags>activeOnClose</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMsF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;Aggregate for window 1752388528298&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.mylyn.java.ui.MembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_kL80gF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kL80gV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMsV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMsl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMs1-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings (1 item)&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_kUY-0F-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kUY-0V-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMtF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMtV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMtl-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMt1-zEfC6v511D2elDw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_VY6okF-0EfCHSK8Zq27fFg" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VY6okV-0EfCHSK8Zq27fFg" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMuF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMuV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMul-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_kTlGgF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kTlGgV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMu1-zEfC6v511D2elDw" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFHMvF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFN6YF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xD;&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xD;&#xA;&lt;sorter>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;filteredTreeFindHistory/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_kPzZwF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_kPzZwV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFN6YV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFN6Yl-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFPvkF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFPvkV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_kFPvkl-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.svg" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_jrUzs1-zEfC6v511D2elDw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_j8CwQF-zEfC6v511D2elDw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8CwQV-zEfC6v511D2elDw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8CwQl-zEfC6v511D2elDw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_BzlnwF-0EfCHSK8Zq27fFg" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.svg" tooltip="Print" command="_jsSeWl-zEfC6v511D2elDw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8CwQ1-zEfC6v511D2elDw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8CwRF-zEfC6v511D2elDw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhQF-zEfC6v511D2elDw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_Bzm14F-0EfCHSK8Zq27fFg" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.svg" tooltip="Undo" enabled="false" command="_jsQoEV-zEfC6v511D2elDw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_Bzm14V-0EfCHSK8Zq27fFg" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.svg" tooltip="Redo" enabled="false" command="_jsSdMl-zEfC6v511D2elDw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhQV-zEfC6v511D2elDw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8MhQl-zEfC6v511D2elDw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kG6jgF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kGGEIF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_kGuWQF-zEfC6v511D2elDw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhQ1-zEfC6v511D2elDw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8MhRF-zEfC6v511D2elDw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhRV-zEfC6v511D2elDw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_Bznc8l-0EfCHSK8Zq27fFg" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.svg" tooltip="Pin Editor" type="Check" command="_jsSeFF-zEfC6v511D2elDw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhRl-zEfC6v511D2elDw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8MhR1-zEfC6v511D2elDw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhSF-zEfC6v511D2elDw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_j8MhSV-zEfC6v511D2elDw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_j8MhSl-zEfC6v511D2elDw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_j-mVAF-zEfC6v511D2elDw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_j-mVAV-zEfC6v511D2elDw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_jrUztF-zEfC6v511D2elDw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_jrUztV-zEfC6v511D2elDw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_jrUztl-zEfC6v511D2elDw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_jrUzt1-zEfC6v511D2elDw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_jrUzuF-zEfC6v511D2elDw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_kZux0F-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_jrUzuV-zEfC6v511D2elDw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_jrUzul-zEfC6v511D2elDw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_jrUzu1-zEfC6v511D2elDw">
    <bindings xmi:id="_js3EzF-zEfC6v511D2elDw" keySequence="CTRL+1" command="_jsQn11-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S-F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+I" command="_jsIs4F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hB1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+L" command="_jsSej1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IEV-zEfC6v511D2elDw" keySequence="CTRL+SPACE" command="_jsSeL1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IGF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+D" command="_jsSesF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WTV-zEfC6v511D2elDw" keySequence="CTRL+V" command="_jsIsYF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WZV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+SPACE" command="_jsQn9F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WZl-zEfC6v511D2elDw" keySequence="CTRL+A" command="_jsSdDV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WdF-zEfC6v511D2elDw" keySequence="CTRL+C" command="_jsSdYV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WlV-zEfC6v511D2elDw" keySequence="CTRL+X" command="_jsQoF1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wll-zEfC6v511D2elDw" keySequence="CTRL+Y" command="_jsSdMl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WmV-zEfC6v511D2elDw" keySequence="CTRL+Z" command="_jsQoEV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nrV-zEfC6v511D2elDw" keySequence="ALT+PAGE_UP" command="_jsSdPV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nrl-zEfC6v511D2elDw" keySequence="ALT+PAGE_DOWN" command="_jsSd5F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nsl-zEfC6v511D2elDw" keySequence="SHIFT+INSERT" command="_jsIsYF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nt1-zEfC6v511D2elDw" keySequence="ALT+F11" command="_jsIspF-zEfC6v511D2elDw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_js_n8F-zEfC6v511D2elDw" keySequence="CTRL+F10" command="_jsIsgl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n91-zEfC6v511D2elDw" keySequence="CTRL+INSERT" command="_jsSdYV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD5F-zEfC6v511D2elDw" keySequence="CTRL+PAGE_UP" command="_jsSecV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD5V-zEfC6v511D2elDw" keySequence="CTRL+PAGE_DOWN" command="_jsQn31-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD51-zEfC6v511D2elDw" keySequence="ALT+SHIFT+F1" command="_jsIsuF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD6F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+F2" command="_jsSdzV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD6V-zEfC6v511D2elDw" keySequence="ALT+SHIFT+F3" command="_jsSeZF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD7l-zEfC6v511D2elDw" keySequence="SHIFT+DEL" command="_jsQoF1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_jsvwAF-zEfC6v511D2elDw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_jsSe-1-zEfC6v511D2elDw">
    <bindings xmi:id="_js0ogF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+CR" command="_jsSeY1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js12oF-zEfC6v511D2elDw" keySequence="CTRL+BS" command="_jsIsNV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js3EyV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+Q" command="_jsIs5l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S-1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+J" command="_jsIs21-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hA1-zEfC6v511D2elDw" keySequence="CTRL++" command="_jsSdxV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hDV-zEfC6v511D2elDw" keySequence="CTRL+-" command="_jsQoTV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IF1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+C" command="_jsIsRF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IH1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F" command="_jsQn_l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WNl-zEfC6v511D2elDw" keySequence="ALT+CTRL+J" command="_jsIs9l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WPl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+A" command="_jsSdfV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WSl-zEfC6v511D2elDw" keySequence="CTRL+T" command="_jsSew1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WUl-zEfC6v511D2elDw" keySequence="CTRL+J" command="_jsIsil-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WVl-zEfC6v511D2elDw" keySequence="CTRL+L" command="_jsSeQl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WXl-zEfC6v511D2elDw" keySequence="CTRL+O" command="_jsSeqV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WY1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+/" command="_jsQoBl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wel-zEfC6v511D2elDw" keySequence="CTRL+D" command="_jsIslV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wj1-zEfC6v511D2elDw" keySequence="CTRL+=" command="_jsSdxV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wk1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Y" command="_jsIsKl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WnF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+DEL" command="_jsSeNF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WnV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+X" command="_jsSdZ1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wnl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+Y" command="_jsQoSl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_no1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+\" command="_jsSdkV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_npF-zEfC6v511D2elDw" keySequence="CTRL+DEL" command="_jsQoC1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_np1-zEfC6v511D2elDw" keySequence="ALT+ARROW_UP" command="_jsSe6V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nqV-zEfC6v511D2elDw" keySequence="ALT+ARROW_DOWN" command="_jsSd8F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nr1-zEfC6v511D2elDw" keySequence="SHIFT+END" command="_jsQoVl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nuF-zEfC6v511D2elDw" keySequence="SHIFT+HOME" command="_jsQoOl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nvl-zEfC6v511D2elDw" keySequence="END" command="_jsSefl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nwF-zEfC6v511D2elDw" keySequence="INSERT" command="_jsSdm1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nxl-zEfC6v511D2elDw" keySequence="F2" command="_jsQn4V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n0l-zEfC6v511D2elDw" keySequence="HOME" command="_jsSemV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n1V-zEfC6v511D2elDw" keySequence="ALT+CTRL+ARROW_UP" command="_jsSexV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n11-zEfC6v511D2elDw" keySequence="ALT+CTRL+ARROW_DOWN" command="_jsSdGF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n3F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+INSERT" command="_jsIsyV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n4V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_jsQoWl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n41-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_jsIs0F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n8V-zEfC6v511D2elDw" keySequence="CTRL+F10" command="_jsSeXl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n9V-zEfC6v511D2elDw" keySequence="CTRL+END" command="_jsSd8l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtBc0V-zEfC6v511D2elDw" keySequence="CTRL+ARROW_UP" command="_jsIstV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD4F-zEfC6v511D2elDw" keySequence="CTRL+ARROW_DOWN" command="_jsSe9V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD4l-zEfC6v511D2elDw" keySequence="CTRL+ARROW_LEFT" command="_jsSdW1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD41-zEfC6v511D2elDw" keySequence="CTRL+ARROW_RIGHT" command="_jsIs5F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD5l-zEfC6v511D2elDw" keySequence="CTRL+HOME" command="_jsIsXl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD6l-zEfC6v511D2elDw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_jsSeAV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD61-zEfC6v511D2elDw" keySequence="CTRL+NUMPAD_ADD" command="_jsSetV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD7F-zEfC6v511D2elDw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_jsSeYV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD7V-zEfC6v511D2elDw" keySequence="CTRL+NUMPAD_DIVIDE" command="_jsIsuV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD9V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_jsSeCV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD-V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_jsSdnV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSF1-zEfC6v511D2elDw" keySequence="ALT+/" command="_jsSenV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSG1-zEfC6v511D2elDw" keySequence="SHIFT+CR" command="_jsSemF-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js12oV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_jsSfDF-zEfC6v511D2elDw">
    <bindings xmi:id="_js12ol-zEfC6v511D2elDw" keySequence="CTRL+CR" command="_jsQoAV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WRV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+C" command="_jsSdSV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wa1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsQoWF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wfl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+U" command="_jsSd_F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WiV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+I" command="_jsQoTl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_npV-zEfC6v511D2elDw" keySequence="ALT+ARROW_UP" command="_jsSd01-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nqF-zEfC6v511D2elDw" keySequence="ALT+ARROW_DOWN" command="_jsQoBV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nsF-zEfC6v511D2elDw" keySequence="SHIFT+INSERT" command="_jsIsql-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nv1-zEfC6v511D2elDw" keySequence="INSERT" command="_jsQoR1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nzF-zEfC6v511D2elDw" keySequence="F4" command="_jsIsfl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n6V-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_UP" command="_jsSeJF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n7V-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_jsQoU1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js12o1-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.window" bindingContext="_jrUzvF-zEfC6v511D2elDw">
    <bindings xmi:id="_js12pF-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+T" command="_jsIsgV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js12pV-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+L" command="_jsSdkF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js3ExV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q O" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js3Exl-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_js3Ex1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q P" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js3EyF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_js4S4F-zEfC6v511D2elDw" keySequence="ALT+CTRL+B" command="_jsSd3l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S4V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+R" command="_jsSe91-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S4l-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q Q" command="_jsSd1V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S41-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+S" command="_jsSdul-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S5F-zEfC6v511D2elDw" keySequence="CTRL+3" command="_jsQn4F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S5V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+T" command="_jsQoFl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S51-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+T" command="_jsQoIF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S6F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q S" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js4S6V-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_js4S6l-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+U" command="_jsIsyF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S61-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q T" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js4S7F-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_js4S7V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+V" command="_jsSegV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S8F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q V" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js4S8V-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_js4S9V-zEfC6v511D2elDw" keySequence="ALT+CTRL+G" command="_jsSdzF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S9l-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+W" command="_jsQoFV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S91-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+H" command="_jsSdVV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S-V-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q H" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js4S-l-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_js5hAF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q J" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js5hAV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_js5hAl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+K" command="_jsIssl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hBF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q K" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js5hBV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_js5hBl-zEfC6v511D2elDw" keySequence="CTRL+," command="_jsIsZF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hC1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q L" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js5hDF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_js5hDl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+N" command="_jsSdcV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hD1-zEfC6v511D2elDw" keySequence="CTRL+." command="_jsSeyF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hEl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+O" command="_jsSerV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IEF-zEfC6v511D2elDw" keySequence="ALT+CTRL+P" command="_jsIsp1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IEl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+B" command="_jsIss1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IE1-zEfC6v511D2elDw" keySequence="CTRL+#" command="_jsIsg1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IGl-zEfC6v511D2elDw" keySequence="ALT+CTRL+T" command="_jsSdXF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IG1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+E" command="_jsIsw1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IIF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+G" command="_jsSe1V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WMF-zEfC6v511D2elDw" keySequence="ALT+CTRL+H" command="_jsIseV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WMl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q X" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7WM1-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_js7WNF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q Y" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7WNV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_js7WN1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q Z" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7WOF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_js7WPF-zEfC6v511D2elDw" keySequence="CTRL+P" command="_jsSeWl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WPV-zEfC6v511D2elDw" keySequence="CTRL+Q" command="_jsSeal-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WRF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+C" command="_jsSeM1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WR1-zEfC6v511D2elDw" keySequence="CTRL+S" command="_jsQoT1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WS1-zEfC6v511D2elDw" keySequence="CTRL+U" command="_jsSdLl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WTF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+F" command="_jsSebl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WT1-zEfC6v511D2elDw" keySequence="CTRL+W" command="_jsSdOF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WUF-zEfC6v511D2elDw" keySequence="CTRL+H" command="_jsSeLl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WU1-zEfC6v511D2elDw" keySequence="CTRL+K" command="_jsSd4l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WV1-zEfC6v511D2elDw" keySequence="CTRL+M" command="_jsSeK1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WWl-zEfC6v511D2elDw" keySequence="CTRL+N" command="_jsSe2F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WZF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+P" command="_jsSdz1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WaF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsSdNV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WbV-zEfC6v511D2elDw" keySequence="CTRL+B" command="_jsIsaF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wbl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q B" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7Wb1-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_js7WdV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+S" command="_jsSdS1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wd1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+T" command="_jsSdf1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WeF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q C" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7WeV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_js7We1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Q D" command="_jsSd1V-zEfC6v511D2elDw">
      <parameters xmi:id="_js7WfF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_js7WgF-zEfC6v511D2elDw" keySequence="CTRL+E" command="_jsQoCV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WgV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+V" command="_jsQoWV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wgl-zEfC6v511D2elDw" keySequence="CTRL+F" command="_jsIsn1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WhV-zEfC6v511D2elDw" keySequence="CTRL+G" command="_jsIsNl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Whl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+W" command="_jsSexF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wh1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+H" command="_jsQoA1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WiF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+I" command="_jsIshF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wi1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+J" command="_jsQoB1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WjF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+K" command="_jsQoUl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WjV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+L" command="_jsQn0l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wjl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+M" command="_jsSetl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WkF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+N" command="_jsQoEl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WlF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+Z" command="_jsSdUF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wml-zEfC6v511D2elDw" keySequence="CTRL+_" command="_jsQn91-zEfC6v511D2elDw">
      <parameters xmi:id="_js7Wm1-zEfC6v511D2elDw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_js_noF-zEfC6v511D2elDw" keySequence="CTRL+{" command="_jsQn91-zEfC6v511D2elDw">
      <parameters xmi:id="_js_noV-zEfC6v511D2elDw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_js_nql-zEfC6v511D2elDw" keySequence="ALT+ARROW_LEFT" command="_jsIshl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nrF-zEfC6v511D2elDw" keySequence="ALT+ARROW_RIGHT" command="_jsQoKl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_ntF-zEfC6v511D2elDw" keySequence="SHIFT+F2" command="_jsSdrV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_ntV-zEfC6v511D2elDw" keySequence="SHIFT+F5" command="_jsSdHV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_ntl-zEfC6v511D2elDw" keySequence="ALT+F7" command="_jsSdhV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nuV-zEfC6v511D2elDw" keySequence="ALT+F5" command="_jsSdCF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nvF-zEfC6v511D2elDw" keySequence="F11" command="_jsSeqF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nvV-zEfC6v511D2elDw" keySequence="F12" command="_jsSeMV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nxV-zEfC6v511D2elDw" keySequence="F2" command="_jsIsZ1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nyF-zEfC6v511D2elDw" keySequence="F3" command="_jsIs91-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nyl-zEfC6v511D2elDw" keySequence="F4" command="_jsIscV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n0F-zEfC6v511D2elDw" keySequence="F5" command="_jsQoMl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n01-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F7" command="_jsSeq1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n1F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F8" command="_jsQn9l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n1l-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F9" command="_jsSdBV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n2F-zEfC6v511D2elDw" keySequence="ALT+CTRL+ARROW_LEFT" command="_jsSeal-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n2V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F11" command="_jsSdTl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n2l-zEfC6v511D2elDw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_jsIsu1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n21-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F12" command="_jsIsSl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n3V-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F4" command="_jsQoFV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n3l-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F6" command="_jsSdwl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n4l-zEfC6v511D2elDw" keySequence="ALT+SHIFT+X J" command="_jsSd6V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n5F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+X M" command="_jsSdFV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n5V-zEfC6v511D2elDw" keySequence="ALT+SHIFT+X A" command="_jsIsYl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n5l-zEfC6v511D2elDw" keySequence="CTRL+F7" command="_jsSdYl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n6l-zEfC6v511D2elDw" keySequence="CTRL+F8" command="_jsQn2V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n7l-zEfC6v511D2elDw" keySequence="CTRL+F9" command="_jsIsx1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n8l-zEfC6v511D2elDw" keySequence="CTRL+F11" command="_jsSef1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n9F-zEfC6v511D2elDw" keySequence="CTRL+F12" command="_jsIstF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n_F-zEfC6v511D2elDw" keySequence="CTRL+F4" command="_jsSdOF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n_l-zEfC6v511D2elDw" keySequence="CTRL+F6" command="_jsIsqF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n_1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+F7" command="_jsSd9F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtBc0F-zEfC6v511D2elDw" keySequence="ALT+CTRL+X G" command="_jsSepF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD71-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_jsSdql-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD8F-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_jsSe5V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD8V-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_jsSdl1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD9F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_jsSduV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD9l-zEfC6v511D2elDw" keySequence="ALT+SHIFT+X Q" command="_jsIs3l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD91-zEfC6v511D2elDw" keySequence="ALT+SHIFT+X T" command="_jsSdC1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD-F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_jsQn-V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD-l-zEfC6v511D2elDw" keySequence="ALT+CTRL+SHIFT+F12" command="_jsSet1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSAF-zEfC6v511D2elDw" keySequence="DEL" command="_jsIsrV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSFl-zEfC6v511D2elDw" keySequence="ALT+-" command="_jsSdcF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSGF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E E" command="_jsIsh1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSGV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E G" command="_jsQoHV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSGl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E J" command="_jsIsX1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSHF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E S" command="_jsSdDl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSHV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E T" command="_jsIsf1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5EF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E L" command="_jsIsZV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5EV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E N" command="_jsSezF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5El-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E P" command="_jsIsMF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5FV-zEfC6v511D2elDw" keySequence="ALT+CR" command="_jsSeIF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5Fl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+E R" command="_jsIs6V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5F1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+D A" command="_jsSeRF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5GF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+D T" command="_jsIsQF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5GV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+D J" command="_jsSeDF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtD5Gl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+D Q" command="_jsSdJF-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js12pl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_jsSfCl-zEfC6v511D2elDw">
    <bindings xmi:id="_js12p1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+P" command="_jsSd81-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S5l-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+T" command="_jsQoFl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js4S8l-zEfC6v511D2elDw" keySequence="CTRL+7" command="_jsSddF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hCF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+M" command="_jsQn4l-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hEF-zEfC6v511D2elDw" keySequence="CTRL+/" command="_jsSddF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IFF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+C" command="_jsSddF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IHl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F" command="_jsSel1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WQV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+B" command="_jsSe7V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WSV-zEfC6v511D2elDw" keySequence="CTRL+T" command="_jsSde1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WUV-zEfC6v511D2elDw" keySequence="CTRL+I" command="_jsQoKF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WXV-zEfC6v511D2elDw" keySequence="CTRL+O" command="_jsSdD1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WYl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+/" command="_jsSdL1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WaV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsSdNV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WfV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+U" command="_jsSeE1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wg1-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+'" command="_jsSdil-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wkl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+O" command="_jsQoDF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nol-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+\" command="_jsIsll-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n31-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+ARROW_UP" command="_jsSdOl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n4F-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_jsSdE1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n51-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_UP" command="_jsSdI1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n61-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_jsIstl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n71-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_jsQoMV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n81-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_jsIskF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n-1-zEfC6v511D2elDw" keySequence="CTRL+F3" command="_jsSex1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSBl-zEfC6v511D2elDw" keySequence="CTRL+2 F" command="_jsSetF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSEl-zEfC6v511D2elDw" keySequence="CTRL+2 R" command="_jsSeIl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSE1-zEfC6v511D2elDw" keySequence="CTRL+2 T" command="_jsSdc1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSFF-zEfC6v511D2elDw" keySequence="CTRL+2 L" command="_jsIsi1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSFV-zEfC6v511D2elDw" keySequence="CTRL+2 M" command="_jsQoNV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js3EwV-zEfC6v511D2elDw" elementId="org.eclipse.core.runtime.xml" bindingContext="_js3EwF-zEfC6v511D2elDw">
    <bindings xmi:id="_js3Ewl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+P" command="_jsSeMl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IGV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+D" command="_jsSdLV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js3Ew1-zEfC6v511D2elDw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_jsSfBF-zEfC6v511D2elDw">
    <bindings xmi:id="_js3ExF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+P" command="_jsQoRF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IIV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+G" command="_jsSefV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WMV-zEfC6v511D2elDw" keySequence="ALT+CTRL+H" command="_jsSeOl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wal-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsIsZ1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nyV-zEfC6v511D2elDw" keySequence="F3" command="_jsSea1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_ny1-zEfC6v511D2elDw" keySequence="F4" command="_jsIsMl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n6F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_UP" command="_jsSdgF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n7F-zEfC6v511D2elDw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_jsSdg1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js3Eyl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_jsSfBV-zEfC6v511D2elDw">
    <bindings xmi:id="_js3Ey1-zEfC6v511D2elDw" keySequence="CTRL+1" command="_jsSe0l-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js4S7l-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_jsSe-V-zEfC6v511D2elDw">
    <bindings xmi:id="_js4S71-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+V" command="_jsQoQl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IFl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+C" command="_jsSeGV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_npl-zEfC6v511D2elDw" keySequence="ALT+ARROW_UP" command="_jsIsM1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nq1-zEfC6v511D2elDw" keySequence="ALT+ARROW_RIGHT" command="_jsSep1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nsV-zEfC6v511D2elDw" keySequence="SHIFT+INSERT" command="_jsQoQl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n9l-zEfC6v511D2elDw" keySequence="CTRL+INSERT" command="_jsSeGV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js4S81-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_jsbm8F-zEfC6v511D2elDw">
    <bindings xmi:id="_js4S9F-zEfC6v511D2elDw" keySequence="CTRL+7" command="_jsSddF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js5hEV-zEfC6v511D2elDw" keySequence="CTRL+/" command="_jsSddF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js6IFV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+C" command="_jsSddF-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js5hCV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_jsSfDV-zEfC6v511D2elDw">
    <bindings xmi:id="_js5hCl-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+M" command="_jsIsT1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WRl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+C" command="_jsSdSV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WX1-zEfC6v511D2elDw" keySequence="CTRL+O" command="_jsSevl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WbF-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsQoWF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wdl-zEfC6v511D2elDw" keySequence="ALT+SHIFT+S" command="_jsQoHF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wf1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+U" command="_jsSd_F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7Wil-zEfC6v511D2elDw" keySequence="ALT+SHIFT+I" command="_jsQoTl-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js5hE1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_jsSfEl-zEfC6v511D2elDw">
    <bindings xmi:id="_js5hFF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+O" command="_jsIsWV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js6IHF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_jsSfEV-zEfC6v511D2elDw">
    <bindings xmi:id="_js6IHV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+F" command="_jsSel1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WZ1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+R" command="_jsIscF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WkV-zEfC6v511D2elDw" keySequence="ALT+SHIFT+O" command="_jsIsPV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_ns1-zEfC6v511D2elDw" keySequence="SHIFT+F2" command="_jsSdjF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nx1-zEfC6v511D2elDw" keySequence="F3" command="_jsIsQ1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WOV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_jsSfA1-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WOl-zEfC6v511D2elDw" keySequence="ALT+CTRL+M" command="_jsSdCl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WO1-zEfC6v511D2elDw" keySequence="ALT+CTRL+N" command="_jsSeuF-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WSF-zEfC6v511D2elDw" keySequence="CTRL+T" command="_jsQn5F-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WTl-zEfC6v511D2elDw" keySequence="CTRL+W" command="_jsSdo1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WWV-zEfC6v511D2elDw" keySequence="CTRL+N" command="_jsSdyF-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WP1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_jsSfBl-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WQF-zEfC6v511D2elDw" keySequence="CTRL+R" command="_jsSdZV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nul-zEfC6v511D2elDw" keySequence="F7" command="_jsSezV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nu1-zEfC6v511D2elDw" keySequence="F8" command="_jsSdll-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_nz1-zEfC6v511D2elDw" keySequence="F5" command="_jsIsdV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n0V-zEfC6v511D2elDw" keySequence="F6" command="_jsQoW1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n-l-zEfC6v511D2elDw" keySequence="CTRL+F2" command="_jsSeN1-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js_n_V-zEfC6v511D2elDw" keySequence="CTRL+F5" command="_jsSeo1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WQl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_jsSfCV-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WQ1-zEfC6v511D2elDw" keySequence="ALT+SHIFT+B" command="_jsSe7V-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WVF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_jsSfB1-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WVV-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+," command="_jsSebV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WWF-zEfC6v511D2elDw" keySequence="CTRL+SHIFT+." command="_jsSeKV-zEfC6v511D2elDw"/>
    <bindings xmi:id="_js7WhF-zEfC6v511D2elDw" keySequence="CTRL+G" command="_jsSeKl-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WW1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_jsSe_F-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WXF-zEfC6v511D2elDw" keySequence="CTRL+O" command="_jsSdIl-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WYF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_jsSfD1-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WYV-zEfC6v511D2elDw" keySequence="CTRL+O" command="_jsIsWV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7WcF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_jsbm8V-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WcV-zEfC6v511D2elDw" keySequence="CTRL+C" command="_jsQn6V-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtCD4V-zEfC6v511D2elDw" keySequence="CTRL+ARROW_LEFT" command="_jsIskl-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7Wcl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_jsSfCF-zEfC6v511D2elDw">
    <bindings xmi:id="_js7Wc1-zEfC6v511D2elDw" keySequence="CTRL+C" command="_jsIsqV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js7Wl1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.console" bindingContext="_jsSe_l-zEfC6v511D2elDw">
    <bindings xmi:id="_js7WmF-zEfC6v511D2elDw" keySequence="CTRL+Z" command="_jsSewV-zEfC6v511D2elDw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_js_nwV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_jsSfDl-zEfC6v511D2elDw">
    <bindings xmi:id="_js_nwl-zEfC6v511D2elDw" keySequence="F1" command="_jsIsQV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js_nw1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_jsbm8l-zEfC6v511D2elDw">
    <bindings xmi:id="_js_nxF-zEfC6v511D2elDw" keySequence="F2" command="_jsIsr1-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js_nzV-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_jsSfEF-zEfC6v511D2elDw">
    <bindings xmi:id="_js_nzl-zEfC6v511D2elDw" keySequence="F5" command="_jsSelV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_js_n-F-zEfC6v511D2elDw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_jsSfAl-zEfC6v511D2elDw">
    <bindings xmi:id="_js_n-V-zEfC6v511D2elDw" keySequence="CTRL+INSERT" command="_jsSdtV-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_jtCD8l-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_jsSfC1-zEfC6v511D2elDw">
    <bindings xmi:id="_jtCD81-zEfC6v511D2elDw" keySequence="ALT+Y" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSAV-zEfC6v511D2elDw" keySequence="ALT+A" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSAl-zEfC6v511D2elDw" keySequence="ALT+B" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSA1-zEfC6v511D2elDw" keySequence="ALT+C" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSBF-zEfC6v511D2elDw" keySequence="ALT+D" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSBV-zEfC6v511D2elDw" keySequence="ALT+E" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSB1-zEfC6v511D2elDw" keySequence="ALT+F" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSCF-zEfC6v511D2elDw" keySequence="ALT+G" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSCV-zEfC6v511D2elDw" keySequence="ALT+P" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSCl-zEfC6v511D2elDw" keySequence="ALT+R" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSC1-zEfC6v511D2elDw" keySequence="ALT+S" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSDF-zEfC6v511D2elDw" keySequence="ALT+T" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSDV-zEfC6v511D2elDw" keySequence="ALT+V" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSDl-zEfC6v511D2elDw" keySequence="ALT+W" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSD1-zEfC6v511D2elDw" keySequence="ALT+H" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSEF-zEfC6v511D2elDw" keySequence="ALT+L" command="_jsSdbl-zEfC6v511D2elDw"/>
    <bindings xmi:id="_jtDSEV-zEfC6v511D2elDw" keySequence="ALT+N" command="_jsSdbl-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_jtD5E1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_jsSe-l-zEfC6v511D2elDw">
    <bindings xmi:id="_jtD5FF-zEfC6v511D2elDw" keySequence="ALT+CR" command="_jsSdVF-zEfC6v511D2elDw"/>
  </bindingTables>
  <bindingTables xmi:id="_kFEwcV-zEfC6v511D2elDw" bindingContext="_kFEwcF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwc1-zEfC6v511D2elDw" bindingContext="_kFEwcl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwdV-zEfC6v511D2elDw" bindingContext="_kFEwdF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwd1-zEfC6v511D2elDw" bindingContext="_kFEwdl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEweV-zEfC6v511D2elDw" bindingContext="_kFEweF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwe1-zEfC6v511D2elDw" bindingContext="_kFEwel-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwfV-zEfC6v511D2elDw" bindingContext="_kFEwfF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwf1-zEfC6v511D2elDw" bindingContext="_kFEwfl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwgV-zEfC6v511D2elDw" bindingContext="_kFEwgF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwg1-zEfC6v511D2elDw" bindingContext="_kFEwgl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwhV-zEfC6v511D2elDw" bindingContext="_kFEwhF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwh1-zEfC6v511D2elDw" bindingContext="_kFEwhl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwiV-zEfC6v511D2elDw" bindingContext="_kFEwiF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwi1-zEfC6v511D2elDw" bindingContext="_kFEwil-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwjV-zEfC6v511D2elDw" bindingContext="_kFEwjF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFEwj1-zEfC6v511D2elDw" bindingContext="_kFEwjl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-kV-zEfC6v511D2elDw" bindingContext="_kFF-kF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-k1-zEfC6v511D2elDw" bindingContext="_kFF-kl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-lV-zEfC6v511D2elDw" bindingContext="_kFF-lF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-l1-zEfC6v511D2elDw" bindingContext="_kFF-ll-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-mV-zEfC6v511D2elDw" bindingContext="_kFF-mF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-m1-zEfC6v511D2elDw" bindingContext="_kFF-ml-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-nV-zEfC6v511D2elDw" bindingContext="_kFF-nF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-n1-zEfC6v511D2elDw" bindingContext="_kFF-nl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-oV-zEfC6v511D2elDw" bindingContext="_kFF-oF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-o1-zEfC6v511D2elDw" bindingContext="_kFF-ol-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-pV-zEfC6v511D2elDw" bindingContext="_kFF-pF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-p1-zEfC6v511D2elDw" bindingContext="_kFF-pl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-qV-zEfC6v511D2elDw" bindingContext="_kFF-qF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-q1-zEfC6v511D2elDw" bindingContext="_kFF-ql-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-rV-zEfC6v511D2elDw" bindingContext="_kFF-rF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-r1-zEfC6v511D2elDw" bindingContext="_kFF-rl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-sV-zEfC6v511D2elDw" bindingContext="_kFF-sF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-s1-zEfC6v511D2elDw" bindingContext="_kFF-sl-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-tV-zEfC6v511D2elDw" bindingContext="_kFF-tF-zEfC6v511D2elDw"/>
  <bindingTables xmi:id="_kFF-t1-zEfC6v511D2elDw" bindingContext="_kFF-tl-zEfC6v511D2elDw"/>
  <rootContext xmi:id="_jrUzu1-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_jrUzvF-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_jrUzvV-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_jsSe-V-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_jsSe-l-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_jsSe-1-zEfC6v511D2elDw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_jsSe_F-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_jsSfBF-zEfC6v511D2elDw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_jsSfBV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_jsSfCl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_jsSfDV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_jsSfDl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_jsSfD1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_jsSfEl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_jsSfEV-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_jsbm8F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_jsSe_l-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_jsSe_1-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_jsSfAF-zEfC6v511D2elDw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_jsSfAl-zEfC6v511D2elDw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_jsSfA1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_jsSfBl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_jsSfB1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_jsSfE1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_jsSfCF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_jsSfC1-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_jsSfDF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_jsSfEF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_jsbm8V-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_jsbm8l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_jrUzvl-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_jsSe_V-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_jsSfAV-zEfC6v511D2elDw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_jsSfCV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_js3EwF-zEfC6v511D2elDw" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_kFEwcF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_kFEwcl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_kFEwdF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_kFEwdl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_kFEweF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_kFEwel-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_kFEwfF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_kFEwfl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_kFEwgF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_kFEwgl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_kFEwhF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_kFEwhl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_kFEwiF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_kFEwil-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_kFEwjF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_kFEwjl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_kFF-kF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_kFF-kl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_kFF-lF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_kFF-ll-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_kFF-mF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_kFF-ml-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_kFF-nF-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_kFF-nl-zEfC6v511D2elDw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_kFF-oF-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_kFF-ol-zEfC6v511D2elDw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_kFF-pF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_kFF-pl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_kFF-qF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_kFF-ql-zEfC6v511D2elDw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_kFF-rF-zEfC6v511D2elDw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_kFF-rl-zEfC6v511D2elDw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_kFF-sF-zEfC6v511D2elDw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_kFF-sl-zEfC6v511D2elDw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_kFF-tF-zEfC6v511D2elDw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_kFF-tl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_jwOh0F-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_j6w94F-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.svg" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_j60BMF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_j60oQF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_j60oQV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j60oQl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j60oQ1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j60oRF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j60oRV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j612YF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j612YV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.svg" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j612Yl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j612Y1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j612ZF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_j612ZV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_j63EgF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_j63EgV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_j63Egl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_j63Eg1-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_j63EhF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63EhV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ehl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.svg" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_j63Eh1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.svg" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63EiF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.svg" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63EiV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.svg" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63Eil-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.svg" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ei1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.svg" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_j63EjF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.svg" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_j63EjV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.svg" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ejl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.svg" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ej1-zEfC6v511D2elDw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j63EkF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.svg" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63EkV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.svg" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ekl-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ek1-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_j63ElF-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_j63ElV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_j63Ell-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_j63El1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZQF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZQV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZQl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZQ1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZRF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.svg" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZRV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.svg" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZRl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZR1-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.presentation.NotificationView" label="Notification" iconURI="platform:/plugin/org.eclipse.oomph.setup.editor/icons/notification.png" tooltip="" allowMultiple="true" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.setup.presentation.NotificationViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.setup.editor"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZSF-zEfC6v511D2elDw" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.svg" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZSV-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZSl-zEfC6v511D2elDw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZS1-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.svg" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZTF-zEfC6v511D2elDw" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZTV-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZTl-zEfC6v511D2elDw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZT1-zEfC6v511D2elDw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j6-ZUF-zEfC6v511D2elDw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7Ef4F-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.svg" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_j7Ef4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7Ef4l-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7FuAF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7FuAV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7IxUF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7IxUV-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7IxUl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7IxU1-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.svg" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7LNkF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7LNkV-zEfC6v511D2elDw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_j7LNkl-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.svg" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_jsIsKF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsKV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsKl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsK1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsLF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_jsIsLV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_jsIsH1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsLl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsL1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsMF-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsMV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsMl-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsM1-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_jsIsG1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsNF-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsNV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsNl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsN1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsOF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsOV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsOl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsO1-zEfC6v511D2elDw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsPF-zEfC6v511D2elDw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_jsIsPV-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsPl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsP1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsQF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsQV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsQl-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_jsIsIV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsQ1-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsRF-zEfC6v511D2elDw" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_jsIsAl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsRV-zEfC6v511D2elDw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsRl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsR1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsSF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsSV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsSl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsS1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsTF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsTV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsTl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsT1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_jsIsBV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsUF-zEfC6v511D2elDw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsUV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsUl-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsU1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsVF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsVV-zEfC6v511D2elDw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_jsIsVl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsV1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsWF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsWV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsWl-zEfC6v511D2elDw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_jsIsD1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsW1-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsXF-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsXV-zEfC6v511D2elDw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsXl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsX1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsYF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsYV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsYl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsY1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsZF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsZV-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsZl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsZ1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsaF-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsaV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsal-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsa1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsbF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsbV-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsbl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsb1-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIscF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIscV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIscl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsc1-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_jsIsIl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsdF-zEfC6v511D2elDw" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsdV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsdl-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsd1-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIseF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIseV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsel-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIse1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsfF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsfV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_jsIsfl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsf1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsgF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsgV-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_jsIsFF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsgl-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsg1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIshF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIshV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIshl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsh1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsiF-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsiV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsil-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsi1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsjF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsjV-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsjl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsj1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIskF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIskV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIskl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsk1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIslF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIslV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsll-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsl1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsmF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsmV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsml-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsm1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsnF-zEfC6v511D2elDw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_jsIsnV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsnl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsn1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsoF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsoV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIsol-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_jsIso1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIspF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIspV-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIspl-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_jsIsAF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsp1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsqF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsqV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsql-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsq1-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsrF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsrV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsrl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsr1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIssF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIssV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIssl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIss1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIstF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIstV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIstl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIst1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_jsIsDl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsuF-zEfC6v511D2elDw" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_jsIsJV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsuV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsul-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsu1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsvF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsvV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_jsIsJl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsvl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsv1-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIswF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_jsIsE1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIswV-zEfC6v511D2elDw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_jsIswl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsw1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsxF-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsxV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsxl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsx1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsyF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsyV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsyl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsy1-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIszF-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIszV-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIszl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIsz1-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs0F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs0V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_jsIsIV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs0l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs01-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_jsIsB1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIs1F-zEfC6v511D2elDw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_jsIs1V-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs1l-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs11-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs2F-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs2V-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs2l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs21-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs3F-zEfC6v511D2elDw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIs3V-zEfC6v511D2elDw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_jsIs3l-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs31-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs4F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs4l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs41-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs5F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs5V-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs5l-zEfC6v511D2elDw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs51-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs6F-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs6V-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs6l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs61-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs7F-zEfC6v511D2elDw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs7V-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs7l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs71-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs8F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_jsIsEF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs8V-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs8l-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_jsIsGV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsIs81-zEfC6v511D2elDw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_jsIs9F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs9V-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs9l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsIs91-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn0F-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn0V-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn0l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn01-zEfC6v511D2elDw" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_jsIsH1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn1F-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn1V-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn1l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn11-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn2F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn2V-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn2l-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn21-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn3F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn3V-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_jsIsIF-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQn3l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_jsQn31-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn4F-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn4l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn41-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_jsIsBV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn5F-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn5V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn5l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn51-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn6F-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn6V-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn6l-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn61-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn7F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn7V-zEfC6v511D2elDw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_jsIsF1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQn7l-zEfC6v511D2elDw" elementId="url" name="URL"/>
    <parameters xmi:id="_jsQn71-zEfC6v511D2elDw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_jsQn8F-zEfC6v511D2elDw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_jsQn8V-zEfC6v511D2elDw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_jsQn8l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn81-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn9F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn9V-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn9l-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn91-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_jsIsF1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQn-F-zEfC6v511D2elDw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_jsQn-V-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn-l-zEfC6v511D2elDw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn-1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn_F-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn_V-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn_l-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQn_1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoAF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoAV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoAl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoA1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoBF-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoBV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoBl-zEfC6v511D2elDw" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_jsIsAl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoB1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoCF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoCV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoCl-zEfC6v511D2elDw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_jsIsD1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoC1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoDF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoDV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoDl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoD1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoEF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoEV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoEl-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoE1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoFF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoFV-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoFl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoF1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoGF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoGV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoGl-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoG1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoHF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_jsIsBV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoHV-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoHl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoH1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoIF-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoIV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoIl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoI1-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_jsIsE1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQoJF-zEfC6v511D2elDw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_jsQoJV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoJl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoJ1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_jsIsIV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoKF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoKV-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoKl-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoK1-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoLF-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoLV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoLl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoL1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoMF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoMV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoMl-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoM1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoNF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoNV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoNl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_jsIsH1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoN1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoOF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoOV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoOl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoO1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoPF-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoPV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQoPl-zEfC6v511D2elDw" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_jsQoP1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoQF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoQV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoQl-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_jsIsG1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoQ1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoRF-zEfC6v511D2elDw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoRV-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsQoRl-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_jsQoR1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoSF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoSV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoSl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoS1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoTF-zEfC6v511D2elDw" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoTV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoTl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoT1-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoUF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoUV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoUl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoU1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoVF-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_jsIsAF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoVV-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoVl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoV1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoWF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoWV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoWl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoW1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsQoXF-zEfC6v511D2elDw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdAF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_jsIsE1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdAV-zEfC6v511D2elDw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_jsSdAl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdA1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdBF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdBV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdBl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdB1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdCF-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdCV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdCl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdC1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdDF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdDV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdDl-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdD1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdEF-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_jsIsIl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdEV-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_jsSdEl-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_jsSdE1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdFF-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdFV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdFl-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdF1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdGF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdGV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdGl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdG1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdHF-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdHV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdHl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdH1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdIF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdIV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_jsIsJF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdIl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdI1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdJF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdJV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdJl-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_jsIsF1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdJ1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_jsSdKF-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_jsIsGV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdKV-zEfC6v511D2elDw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_jsSdKl-zEfC6v511D2elDw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_jsSdK1-zEfC6v511D2elDw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_jsSdLF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdLV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdLl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdL1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdMF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdMV-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdMl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdM1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdNF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdNV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdNl-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_jsIsFF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdN1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdOF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdOV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdOl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdO1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdPF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_jsSdPV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdPl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdP1-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdQF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_jsSdQV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdQl-zEfC6v511D2elDw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_jsIsHl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdQ1-zEfC6v511D2elDw" elementId="title" name="Title"/>
    <parameters xmi:id="_jsSdRF-zEfC6v511D2elDw" elementId="message" name="Message"/>
    <parameters xmi:id="_jsSdRV-zEfC6v511D2elDw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_jsSdRl-zEfC6v511D2elDw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_jsSdR1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdSF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdSV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdSl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdS1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdTF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdTV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdTl-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdT1-zEfC6v511D2elDw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdUF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdUV-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdUl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdU1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdVF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdVV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdVl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdV1-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdWF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_jsIsBl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdWV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdWl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdW1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdXF-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_jsIsFF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdXV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_jsIsF1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdXl-zEfC6v511D2elDw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_jsSdX1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdYF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdYV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdYl-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdY1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdZF-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdZV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdZl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdZ1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdaF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdaV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdal-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSda1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdbF-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdbV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdbl-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_jsIsG1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdb1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdcF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdcV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdcl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdc1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSddF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSddV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSddl-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdd1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdeF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdeV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdel-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSde1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdfF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdfV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdfl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdf1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdgF-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdgV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdgl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdg1-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdhF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdhV-zEfC6v511D2elDw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdhl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdh1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_jsIsIF-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdiF-zEfC6v511D2elDw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_jsSdiV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdil-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdi1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdjF-zEfC6v511D2elDw" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdjV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdjl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdj1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdkF-zEfC6v511D2elDw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_jsIsFl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdkV-zEfC6v511D2elDw" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_jsIsAl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdkl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdk1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdlF-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdlV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdll-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdl1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdmF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdmV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdml-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdm1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdnF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdnV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdnl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.bugs.commands.ReportBugAction" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement for predefined Products / Projects" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdn1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdoF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdoV-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdol-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_jsSdo1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdpF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdpV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdpl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdp1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdqF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdqV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdql-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdq1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdrF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdrV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdrl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdr1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdsF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdsV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdsl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSds1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_jsIsIV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdtF-zEfC6v511D2elDw" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdtV-zEfC6v511D2elDw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_jsIsD1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdtl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_jsIsA1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSdt1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_jsSduF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSduV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdul-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdu1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdvF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdvV-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdvl-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdv1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdwF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdwV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdwl-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdw1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdxF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdxV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdxl-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdx1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdyF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdyV-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdyl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdy1-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdzF-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdzV-zEfC6v511D2elDw" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_jsIsJV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdzl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSdz1-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd0F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd0V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd0l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd01-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd1F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd1V-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_jsIsAV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd1l-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_jsSd11-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_jsSd2F-zEfC6v511D2elDw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_jsSd2V-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.notifications" commandName="Notifications" description="Notifications for the Eclipse IDE" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd2l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd21-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd3F-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd3V-zEfC6v511D2elDw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_jsSd3l-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd31-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_jsIsEl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd4F-zEfC6v511D2elDw" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_jsSd4V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd4l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd41-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd5F-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd5V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd5l-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_jsIsDF-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd51-zEfC6v511D2elDw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_jsSd6F-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd6V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd6l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd61-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_jsIsB1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd7F-zEfC6v511D2elDw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_jsSd7V-zEfC6v511D2elDw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_jsIsGV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSd7l-zEfC6v511D2elDw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_jsSd71-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd8F-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd8V-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd8l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd81-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd9F-zEfC6v511D2elDw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd9V-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd9l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd91-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd-F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd-V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd-l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd-1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd_F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd_V-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd_l-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSd_1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeAF-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_jsIsFF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeAV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeAl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeA1-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeBF-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeBV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeBl-zEfC6v511D2elDw" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeB1-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeCF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeCV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeCl-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeC1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeDF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeDV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeDl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeD1-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeEF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeEV-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeEl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeE1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeFF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeFV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeFl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeF1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeGF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeGV-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_jsIsG1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeGl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeG1-zEfC6v511D2elDw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeHF-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_jsIsIl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSeHV-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_jsSeHl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeH1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeIF-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeIV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeIl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeI1-zEfC6v511D2elDw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeJF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeJV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeJl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeJ1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeKF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeKV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeKl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeK1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeLF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeLV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeLl-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeL1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeMF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeMV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeMl-zEfC6v511D2elDw" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeM1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeNF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeNV-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeNl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeN1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeOF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeOV-zEfC6v511D2elDw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeOl-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeO1-zEfC6v511D2elDw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSePF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSePV-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSePl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeP1-zEfC6v511D2elDw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_jsIsIV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSeQF-zEfC6v511D2elDw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_jsSeQV-zEfC6v511D2elDw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_jsSeQl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeQ1-zEfC6v511D2elDw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeRF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeRV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeRl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeR1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeSF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeSV-zEfC6v511D2elDw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeSl-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeS1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeTF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeTV-zEfC6v511D2elDw" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeTl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeT1-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_jsIsEl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSeUF-zEfC6v511D2elDw" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_jsSeUV-zEfC6v511D2elDw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeUl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.problem" commandName="Report a Problem" description="Report a problem for this IDE" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeU1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeVF-zEfC6v511D2elDw" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeVV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeVl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeV1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeWF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeWV-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_jsIsIl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeWl-zEfC6v511D2elDw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_jsIsE1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeW1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeXF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeXV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeXl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeX1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeYF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeYV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeYl-zEfC6v511D2elDw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeY1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeZF-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeZV-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeZl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeZ1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeaF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeaV-zEfC6v511D2elDw" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeal-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSea1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSebF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSebV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSebl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeb1-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSecF-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSecV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSecl-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSec1-zEfC6v511D2elDw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_jsIsHl-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSedF-zEfC6v511D2elDw" elementId="title" name="Title"/>
    <parameters xmi:id="_jsSedV-zEfC6v511D2elDw" elementId="message" name="Message"/>
    <parameters xmi:id="_jsSedl-zEfC6v511D2elDw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_jsSed1-zEfC6v511D2elDw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_jsSeeF-zEfC6v511D2elDw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_jsSeeV-zEfC6v511D2elDw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_jsSeel-zEfC6v511D2elDw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_jsSee1-zEfC6v511D2elDw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_jsSefF-zEfC6v511D2elDw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_jsSefV-zEfC6v511D2elDw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSefl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSef1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSegF-zEfC6v511D2elDw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSegV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSegl-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeg1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSehF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSehV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSehl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeh1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_jsIsIV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeiF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeiV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeil-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSei1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSejF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSejV-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_jsSejl-zEfC6v511D2elDw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_jsSej1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSekF-zEfC6v511D2elDw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSekV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSekl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSek1-zEfC6v511D2elDw" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_jsSelF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSelV-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_jsIsAV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSell-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSel1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSemF-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSemV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeml-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_jsIsCV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSem1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSenF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSenV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSenl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSen1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeoF-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_jsIsAF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeoV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_jsIsGl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeol-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeo1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSepF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSepV-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSepl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSep1-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_jsIsG1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeqF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeqV-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeql-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeq1-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSerF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSerV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSerl-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_jsIsA1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSer1-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSesF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSesV-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSesl-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_jsIsFF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSes1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSetF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSetV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSetl-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSet1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeuF-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeuV-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeul-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeu1-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSevF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSevV-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSevl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_jsIsDF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSev1-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSewF-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_jsIsBl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSewV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSewl-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSew1-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_jsIsEV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSexF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSexV-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSexl-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSex1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeyF-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeyV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSeyl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSey1-zEfC6v511D2elDw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_jsSezF-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSezV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSezl-zEfC6v511D2elDw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_jsIsF1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSez1-zEfC6v511D2elDw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_jsSe0F-zEfC6v511D2elDw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_jsSe0V-zEfC6v511D2elDw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_jsIsGV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe0l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe01-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe1F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe1V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe1l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe11-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe2F-zEfC6v511D2elDw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_jsIsE1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSe2V-zEfC6v511D2elDw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_jsSe2l-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe21-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe3F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_jsIsB1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSe3V-zEfC6v511D2elDw" elementId="kind" name="Kind"/>
    <parameters xmi:id="_jsSe3l-zEfC6v511D2elDw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_jsSe31-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe4F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_jsIsB1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe4V-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_jsIsEl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe4l-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe41-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe5F-zEfC6v511D2elDw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe5V-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_jsIsC1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe5l-zEfC6v511D2elDw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe51-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_jsIsIF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe6F-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_jsIsHF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe6V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe6l-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_jsIsJ1-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSe61-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_jsSe7F-zEfC6v511D2elDw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_jsSe7V-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_jsIsDV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe7l-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_jsIsGF-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe71-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_jsIsI1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe8F-zEfC6v511D2elDw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_jsIsFV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe8V-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_jsIsHV-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe8l-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe81-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe9F-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe9V-zEfC6v511D2elDw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_jsIsCl-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe9l-zEfC6v511D2elDw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_jsIsF1-zEfC6v511D2elDw"/>
  <commands xmi:id="_jsSe91-zEfC6v511D2elDw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_jsIsDV-zEfC6v511D2elDw">
    <parameters xmi:id="_jsSe-F-zEfC6v511D2elDw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_j6NkQF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkQV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkQl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkQ1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkRF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkRV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkRl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkR1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkSF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkSV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkSl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkS1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkTF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6NkTV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6TD0F-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6TD0V-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6TD0l-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR8F-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR8V-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR8l-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR81-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR9F-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR9V-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR9l-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR91-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR-F-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR-V-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR-l-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR-1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR_F-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR_V-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR_l-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6UR_1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USAF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USAV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USAl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USA1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USBF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USBV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USBl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USB1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USCF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USCV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USCl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USC1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USDF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USDV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USDl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USD1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USEF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USEV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USEl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USE1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USFF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USFV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USFl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USF1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USGF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USGV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USGl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USG1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USHF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USHV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USHl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USH1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USIF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USIV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USIl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USI1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USJF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USJV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USJl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USJ1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USKF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USKV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USKl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6USK1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_oF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_oV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_ol-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_o1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_pF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.CollapseStackFrames" commandName="Collapse Stack Frames" description="Hide less relevant stack frames" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_pV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_pl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_p1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_qF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_qV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_ql-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_q1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_rF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_rV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_rl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_r1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_sF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_sV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_sl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_s1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_tF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_tV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_tl-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_t1-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_uF-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <commands xmi:id="_j6a_uV-zEfC6v511D2elDw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_jsIsJ1-zEfC6v511D2elDw"/>
  <addons xmi:id="_jrUzv1-zEfC6v511D2elDw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_jrUzwF-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_jrUzwV-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_jrUzwl-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_jrUzw1-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_jrUzxF-zEfC6v511D2elDw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_jrUzxV-zEfC6v511D2elDw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_jrUzxl-zEfC6v511D2elDw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_jrUzx1-zEfC6v511D2elDw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_jrUzyF-zEfC6v511D2elDw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_jrpj0F-zEfC6v511D2elDw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_BpoagV-0EfCHSK8Zq27fFg" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_jsIsAF-zEfC6v511D2elDw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_jsIsAV-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_jsIsAl-zEfC6v511D2elDw" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_jsIsA1-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_jsIsBF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_jsIsBV-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_jsIsBl-zEfC6v511D2elDw" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_jsIsB1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_jsIsCF-zEfC6v511D2elDw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_jsIsCV-zEfC6v511D2elDw" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_jsIsCl-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_jsIsC1-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_jsIsDF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_jsIsDV-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_jsIsDl-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_jsIsD1-zEfC6v511D2elDw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_jsIsEF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_jsIsEV-zEfC6v511D2elDw" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_jsIsEl-zEfC6v511D2elDw" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_jsIsE1-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_jsIsFF-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_jsIsFV-zEfC6v511D2elDw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_jsIsFl-zEfC6v511D2elDw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_jsIsF1-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_jsIsGF-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_jsIsGV-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_jsIsGl-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_jsIsG1-zEfC6v511D2elDw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_jsIsHF-zEfC6v511D2elDw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_jsIsHV-zEfC6v511D2elDw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_jsIsHl-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_jsIsH1-zEfC6v511D2elDw" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_jsIsIF-zEfC6v511D2elDw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_jsIsIV-zEfC6v511D2elDw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_jsIsIl-zEfC6v511D2elDw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_jsIsI1-zEfC6v511D2elDw" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_jsIsJF-zEfC6v511D2elDw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_jsIsJV-zEfC6v511D2elDw" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_jsIsJl-zEfC6v511D2elDw" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_jsIsJ1-zEfC6v511D2elDw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
