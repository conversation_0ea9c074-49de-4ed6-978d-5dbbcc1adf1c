# Healthcare Patient Management System

A comprehensive healthcare management system built with Django, jQuery, PostgreSQL, and managed with Maven.

## 🏥 Project Overview

This system provides a complete solution for healthcare facilities to manage:
- Patient registration and records
- Doctor management and scheduling
- Appointment booking and tracking
- Medical history and prescriptions
- Insurance claims processing
- Analytics and reporting

## 🛠️ Technology Stack

- **Backend:** Django 4.2+ (Python)
- **Frontend:** jQuery 3.6+ with Bootstrap 5
- **Database:** PostgreSQL 14+
- **Build Tool:** Maven 3.8+
- **IDE:** Eclipse IDE for Java Developers
- **Version Control:** SVN

## 📁 Project Structure

```
healthcare-system/
├── backend/                    # Django application
│   ├── healthcare_system/      # Main Django project
│   ├── patients/              # Patient management app
│   ├── doctors/               # Doctor management app
│   ├── appointments/          # Appointment scheduling app
│   ├── medical_records/       # Medical records app
│   └── requirements.txt       # Python dependencies
├── frontend/                  # Frontend assets
│   ├── static/               # Static files (CSS, JS, images)
│   ├── templates/            # HTML templates
│   └── package.json          # Frontend dependencies
├── database/                 # Database scripts
│   ├── schema.sql           # Database schema
│   ├── sample_data.sql      # Sample data for testing
│   └── migrations/          # Database migrations
├── docs/                    # Documentation
│   ├── api/                # API documentation
│   ├── user_guide/         # User manuals
│   └── technical/          # Technical documentation
├── pom.xml                 # Maven configuration
└── README.md              # This file
```

## 🚀 Getting Started

### Prerequisites
- Java 11+
- Python 3.9+
- PostgreSQL 14+
- Eclipse IDE for Java Developers
- Maven 3.8+

### Installation Steps

1. **Clone the repository**
   ```bash
   svn checkout [repository-url] healthcare-system
   cd healthcare-system
   ```

2. **Setup Maven dependencies**
   ```bash
   mvn clean install
   ```

3. **Setup Python virtual environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

4. **Setup PostgreSQL database**
   ```bash
   createdb healthcare_db
   psql healthcare_db < database/schema.sql
   ```

5. **Run Django migrations**
   ```bash
   cd backend
   python manage.py migrate
   python manage.py createsuperuser
   ```

6. **Start the development server**
   ```bash
   python manage.py runserver
   ```

## 📚 Learning Resources

### Maven Basics
- **pom.xml:** Project configuration and dependencies
- **Build lifecycle:** clean → compile → test → package → install
- **Dependencies:** Automatic download and management of libraries
- **Profiles:** Different configurations for dev/prod environments

### jQuery Integration
- **Selectors:** Target HTML elements efficiently
- **AJAX:** Communicate with Django REST APIs
- **Events:** Handle user interactions
- **DOM Manipulation:** Update page content dynamically

### Key Commands

**Maven Commands:**
```bash
mvn clean                    # Clean build artifacts
mvn compile                  # Compile source code
mvn test                     # Run tests
mvn package                  # Create JAR/WAR files
mvn clean install           # Full build cycle
mvn dependency:tree         # Show dependency tree
```

**Django Commands:**
```bash
python manage.py runserver          # Start development server
python manage.py makemigrations     # Create database migrations
python manage.py migrate            # Apply migrations
python manage.py collectstatic      # Collect static files
python manage.py test               # Run tests
```

## 🎯 Development Phases

- [x] **Phase 1:** Environment setup and learning foundation
- [ ] **Phase 2:** Database design and Django backend
- [ ] **Phase 3:** Frontend development with jQuery
- [ ] **Phase 4:** Integration and advanced features
- [ ] **Phase 5:** Testing, documentation, and SVN setup

## 🏆 Features to Impress Josh Technologies

1. **Modern Architecture:** Clean separation of concerns
2. **Scalable Design:** Modular Django apps
3. **Rich UI:** Interactive jQuery interfaces
4. **Data Security:** Healthcare compliance ready
5. **Professional Workflow:** Maven build automation
6. **Industry Standards:** Following best practices

## 📞 Support

For questions or issues, please refer to the documentation in the `docs/` directory.

---

**Built with ❤️ for Josh Technologies Group**
