2025-07-13 12:05:29,982 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is not available. Remote download required.
2025-07-13 12:07:30,201 [Worker-23: Creating project "patient-management-system"] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using NULL lifecycle mapping for MavenProject: com.healthcare:patient-management-system:0.0.1-SNAPSHOT @ D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\patient-management-system\pom.xml.
2025-07-13 12:08:50,019 [Worker-7: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-07-13 12:10:24,665 [main] INFO  o.e.m2e.actions.ExecutePomAction - Creating new launch configuration
2025-07-13 12:10:56,699 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013 [299])
2025-07-13 12:10:56,701 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013
2025-07-13 12:10:56,701 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\aopalliance-1.0.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\guice-5.1.0.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpclient-4.5.14.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpcore-4.4.16.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jansi-2.4.1.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jcl-over-slf4j-1.7.36.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-artifact-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-builder-support-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-compat-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-core-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-embedder-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-3.9.9.jar
2025-07-13 12:10:56,702 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-builder-3.9.9.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-plugin-api-3.9.9.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-repository-metadata-3.9.9.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-api-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-connector-basic-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-impl-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-named-locks-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-provider-3.9.9.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-spi-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-file-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-http-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-wagon-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-util-1.9.22.jar
2025-07-13 12:10:56,703 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-3.9.9.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-builder-3.9.9.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-shared-utils-3.4.2.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.inject-0.9.0.M3.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.plexus-0.9.0.M3.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-0.0.7.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-1.2.0.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-cipher-2.0.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-classworlds-2.8.0.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-component-annotations-2.1.0.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-interpolation-1.27.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-sec-dispatcher-2.0.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-utils-3.5.1.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-xml-3.0.1.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-file-3.5.3.jar
2025-07-13 12:10:56,704 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-3.5.3.jar
2025-07-13 12:10:56,705 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-shared-3.5.3.jar
2025-07-13 12:10:56,705 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-provider-api-3.5.3.jar
2025-07-13 12:10:56,706 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava_33.4.8.jre [13])
2025-07-13 12:10:56,706 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava_33.4.8.jre.jar
2025-07-13 12:10:56,706 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=slf4j.api_2.0.17 [522])
2025-07-13 12:10:56,706 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\slf4j.api_2.0.17.jar
2025-07-13 12:10:56,706 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=jakarta.inject.jakarta.inject-api_1.0.5 [24])
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\jakarta.inject.jakarta.inject-api_1.0.5.jar
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=javax.annotation_1.3.5.v20200909-1856 [27])
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\javax.annotation_1.3.5.v20200909-1856.jar
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.cli_1.9.0 [49])
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.cli_1.9.0.jar
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.commons-codec_1.16.0 [50])
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.commons-codec_1.16.0.jar
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava.failureaccess_1.0.3 [14])
2025-07-13 12:10:56,707 [Worker-11: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava.failureaccess_1.0.3.jar
2025-07-13 12:10:56,710 [Worker-11: Launching patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate - Run build in "D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\patient-management-system":
2025-07-13 12:10:56,714 [Worker-11: Launching patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate -  mvn  -B -Dstyle.color=always clean compile
2025-07-13 12:18:49,813 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-07-13 12:24:56,455 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013 [299])
2025-07-13 12:24:56,456 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\aopalliance-1.0.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\guice-5.1.0.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpclient-4.5.14.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpcore-4.4.16.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jansi-2.4.1.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jcl-over-slf4j-1.7.36.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-artifact-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-builder-support-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-compat-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-core-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-embedder-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-builder-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-plugin-api-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-repository-metadata-3.9.9.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-api-1.9.22.jar
2025-07-13 12:24:56,457 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-connector-basic-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-impl-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-named-locks-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-provider-3.9.9.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-spi-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-file-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-http-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-wagon-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-util-1.9.22.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-3.9.9.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-builder-3.9.9.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-shared-utils-3.4.2.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.inject-0.9.0.M3.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.plexus-0.9.0.M3.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-0.0.7.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-1.2.0.jar
2025-07-13 12:24:56,458 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-cipher-2.0.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-classworlds-2.8.0.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-component-annotations-2.1.0.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-interpolation-1.27.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-sec-dispatcher-2.0.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-utils-3.5.1.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-xml-3.0.1.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-file-3.5.3.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-3.5.3.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-shared-3.5.3.jar
2025-07-13 12:24:56,459 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-provider-api-3.5.3.jar
2025-07-13 12:24:56,460 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava_33.4.8.jre [13])
2025-07-13 12:24:56,460 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava_33.4.8.jre.jar
2025-07-13 12:24:56,460 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=slf4j.api_2.0.17 [522])
2025-07-13 12:24:56,460 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\slf4j.api_2.0.17.jar
2025-07-13 12:24:56,460 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=jakarta.inject.jakarta.inject-api_1.0.5 [24])
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\jakarta.inject.jakarta.inject-api_1.0.5.jar
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=javax.annotation_1.3.5.v20200909-1856 [27])
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\javax.annotation_1.3.5.v20200909-1856.jar
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.cli_1.9.0 [49])
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.cli_1.9.0.jar
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.commons-codec_1.16.0 [50])
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.commons-codec_1.16.0.jar
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava.failureaccess_1.0.3 [14])
2025-07-13 12:24:56,461 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava.failureaccess_1.0.3.jar
2025-07-13 12:24:56,463 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate - Run build in "D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\patient-management-system":
2025-07-13 12:24:56,466 [Worker-20: Launching Executing install in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate -  mvn  -B -Dstyle.color=always install
2025-07-13 12:25:08,819 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013 [299])
2025-07-13 12:25:08,819 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013
2025-07-13 12:25:08,820 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\aopalliance-1.0.jar
2025-07-13 12:25:08,820 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\guice-5.1.0.jar
2025-07-13 12:25:08,820 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpclient-4.5.14.jar
2025-07-13 12:25:08,820 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpcore-4.4.16.jar
2025-07-13 12:25:08,820 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jansi-2.4.1.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jcl-over-slf4j-1.7.36.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-artifact-3.9.9.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-builder-support-3.9.9.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-compat-3.9.9.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-core-3.9.9.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-embedder-3.9.9.jar
2025-07-13 12:25:08,821 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-3.9.9.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-builder-3.9.9.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-plugin-api-3.9.9.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-repository-metadata-3.9.9.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-api-1.9.22.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-connector-basic-1.9.22.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-impl-1.9.22.jar
2025-07-13 12:25:08,822 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-named-locks-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-provider-3.9.9.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-spi-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-file-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-http-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-wagon-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-util-1.9.22.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-3.9.9.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-builder-3.9.9.jar
2025-07-13 12:25:08,823 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-shared-utils-3.4.2.jar
2025-07-13 12:25:08,824 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.inject-0.9.0.M3.jar
2025-07-13 12:25:08,824 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.plexus-0.9.0.M3.jar
2025-07-13 12:25:08,824 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-0.0.7.jar
2025-07-13 12:25:08,824 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-1.2.0.jar
2025-07-13 12:25:08,824 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-cipher-2.0.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-classworlds-2.8.0.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-component-annotations-2.1.0.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-interpolation-1.27.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-sec-dispatcher-2.0.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-utils-3.5.1.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-xml-3.0.1.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-file-3.5.3.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-3.5.3.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-shared-3.5.3.jar
2025-07-13 12:25:08,825 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-provider-api-3.5.3.jar
2025-07-13 12:25:08,826 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava_33.4.8.jre [13])
2025-07-13 12:25:08,826 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava_33.4.8.jre.jar
2025-07-13 12:25:08,826 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=slf4j.api_2.0.17 [522])
2025-07-13 12:25:08,826 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\slf4j.api_2.0.17.jar
2025-07-13 12:25:08,826 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=jakarta.inject.jakarta.inject-api_1.0.5 [24])
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\jakarta.inject.jakarta.inject-api_1.0.5.jar
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=javax.annotation_1.3.5.v20200909-1856 [27])
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\javax.annotation_1.3.5.v20200909-1856.jar
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.cli_1.9.0 [49])
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.cli_1.9.0.jar
2025-07-13 12:25:08,827 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.commons-codec_1.16.0 [50])
2025-07-13 12:25:08,828 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.commons-codec_1.16.0.jar
2025-07-13 12:25:08,828 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava.failureaccess_1.0.3 [14])
2025-07-13 12:25:08,828 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava.failureaccess_1.0.3.jar
2025-07-13 12:25:08,829 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate - Run build in "D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\patient-management-system":
2025-07-13 12:25:08,829 [Worker-10: Launching Executing verify in D__D_DRIVE_Documents_GitHub_josh_healthcare-system_patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate -  mvn  -B -Dstyle.color=always verify
2025-07-13 12:33:56,832 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013 [299])
2025-07-13 12:33:56,832 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013
2025-07-13 12:33:56,832 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\aopalliance-1.0.jar
2025-07-13 12:33:56,832 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\guice-5.1.0.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpclient-4.5.14.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\httpcore-4.4.16.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jansi-2.4.1.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\jcl-over-slf4j-1.7.36.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-artifact-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-builder-support-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-compat-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-core-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-embedder-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-model-builder-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-plugin-api-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-repository-metadata-3.9.9.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-api-1.9.22.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-connector-basic-1.9.22.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-impl-1.9.22.jar
2025-07-13 12:33:56,834 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-named-locks-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-provider-3.9.9.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-spi-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-file-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-http-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-transport-wagon-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-resolver-util-1.9.22.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-3.9.9.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-settings-builder-3.9.9.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\maven-shared-utils-3.4.2.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.inject-0.9.0.M3.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\org.eclipse.sisu.plexus-0.9.0.M3.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-0.0.7.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-build-api-1.2.0.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-cipher-2.0.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-classworlds-2.8.0.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-component-annotations-2.1.0.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-interpolation-1.27.jar
2025-07-13 12:33:56,835 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-sec-dispatcher-2.0.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-utils-3.5.1.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\plexus-xml-3.0.1.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-file-3.5.3.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-3.5.3.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-http-shared-3.5.3.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.eclipse.m2e.maven.runtime_3.9.900.20250220-2013\jars\wagon-provider-api-3.5.3.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava_33.4.8.jre [13])
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava_33.4.8.jre.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=slf4j.api_2.0.17 [522])
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\slf4j.api_2.0.17.jar
2025-07-13 12:33:56,836 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=jakarta.inject.jakarta.inject-api_1.0.5 [24])
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\jakarta.inject.jakarta.inject-api_1.0.5.jar
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=javax.annotation_1.3.5.v20200909-1856 [27])
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\javax.annotation_1.3.5.v20200909-1856.jar
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.cli_1.9.0 [49])
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.cli_1.9.0.jar
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=org.apache.commons.commons-codec_1.16.0 [50])
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\org.apache.commons.commons-codec_1.16.0.jar
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - getClasspathEntries(Bundle=com.google.guava.failureaccess_1.0.3 [14])
2025-07-13 12:33:56,837 [Worker-24: Launching patient-management-system] INFO  o.eclipse.m2e.core.internal.Bundles - 	Entry: C:\Users\<USER>\.p2\pool\plugins\com.google.guava.failureaccess_1.0.3.jar
2025-07-13 12:33:56,838 [Worker-24: Launching patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate - Run build in "D:\D_DRIVE\Documents\GitHub\josh\healthcare-system\patient-management-system":
2025-07-13 12:33:56,838 [Worker-24: Launching patient-management-system] INFO  o.e.m.i.launch.MavenLaunchDelegate -  mvn  -B -Dstyle.color=always clean compile
